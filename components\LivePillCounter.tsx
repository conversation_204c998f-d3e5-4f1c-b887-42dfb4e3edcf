import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, Text, ActivityIndicator, Dimensions, Alert } from 'react-native';
import { CameraView, useCameraPermissions } from 'expo-camera';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useLanguage } from '../contexts/LanguageContext';
import { Colors, Spacing, BorderRadius } from '../constants/PillLogicDesign';
import { countPills, PillCountResult } from '../services/roboflowService';
import PillVisualization from './PillVisualization';
import * as FileSystem from 'expo-file-system';
import { useUsageLimits } from '../hooks/useUsageLimits';
import { FeatureType } from '../lib/supabase';

export default function LivePillCounter() {
  const { t } = useLanguage();
  const [permission, requestPermission] = useCameraPermissions();
  const [facing, setFacing] = useState<'front' | 'back'>('back');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [pillCount, setPillCount] = useState(0);
  const [pillResult, setPillResult] = useState<PillCountResult | null>(null);
  const [currentImageUri, setCurrentImageUri] = useState<string | null>(null);
  // Always in live mode now
  const [isLiveMode, setIsLiveMode] = useState(true);
  const cameraRef = useRef<CameraView | null>(null);
  const router = useRouter();
  const analysisIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastAnalysisTimeRef = useRef<number>(0);
  const ANALYSIS_INTERVAL = 5000; // 5 seconds between analyses

  // Usage limits
  const {
    checkFeatureAccess,
    trackUsage,
    startPillScanSession,
    endPillScanSession,
    isPaidUser,
    isPremiumUser,
    isProUser,
    sessionTimeRemaining,
    activeSession,
    getRemainingUsage,
    FEATURE_LIMITS,
    LIVE_SESSION_TIME_LIMIT
  } = useUsageLimits();

  // Request camera permissions on component mount
  useEffect(() => {
    requestPermission();

    // Check if user can access the feature
    const initComponent = async () => {
      // Premium users always have access to the feature
      if (isPremiumUser) {
        console.log('Premium user accessing live pill count feature');
        // We don't automatically start a session anymore
        // The user will start recording by pressing the record button
        console.log('Component mounted, waiting for user to start recording');
        // Make sure we're not in live mode initially
        setIsLiveMode(false);
        return;
      }

      // For non-premium users, check feature access
      const canUse = await checkFeatureAccess(FeatureType.LIVE_PILL_SCAN);
      if (!canUse) {
        // If user can't use the feature, go back
        router.back();
        return;
      }

      // We don't automatically start a session anymore
      // The user will start recording by pressing the record button
      console.log('Component mounted, waiting for user to start recording');

      // Make sure we're not in live mode initially
      setIsLiveMode(false);
    };

    initComponent();

    // This cleanup function is crucial - it runs when the component unmounts
    return () => {
      console.log('Component unmounting, cleaning up resources');

      // Clean up interval on unmount
      if (analysisIntervalRef.current) {
        console.log('Clearing analysis interval');
        clearInterval(analysisIntervalRef.current);
        analysisIntervalRef.current = null;
      }

      // End the live session if active
      if (activeSession?.id) {
        console.log('Ending active session on unmount:', activeSession.id);

        // Use a synchronous approach for unmounting to ensure it completes
        try {
          // Also try the normal method
          endPillScanSession(activeSession.id)
            .then(success => {
              console.log('Session end result:', success ? 'success' : 'failed');
            })
            .catch(error => {
              console.error('Error ending session on unmount:', error);
            });
        } catch (error) {
          console.error('Error in unmount cleanup:', error);
        }
      } else {
        console.log('No active session to end on unmount');
      }

      // Set live mode to false to ensure clean state
      setIsLiveMode(false);
    };
  }, []);

  // Start/stop live analysis when isLiveMode changes
  useEffect(() => {
    if (isLiveMode) {
      startLiveAnalysis();
    } else {
      stopLiveAnalysis();
    }

    return () => {
      stopLiveAnalysis();
    };
  }, [isLiveMode]);

  // Display time remaining for free users
  useEffect(() => {
    if (!isPaidUser && sessionTimeRemaining !== null && sessionTimeRemaining <= 30) {
      // Show warning when 30 seconds or less remain
      Alert.alert(
        t('sessionTimeWarningTitle'),
        t('sessionTimeWarning', { seconds: sessionTimeRemaining }),
        [{ text: t('ok') }]
      );
    }
  }, [isPaidUser, sessionTimeRemaining]);

  const startLiveAnalysis = () => {
    if (analysisIntervalRef.current) {
      clearInterval(analysisIntervalRef.current);
    }

    // Start the interval for capturing and analyzing frames
    analysisIntervalRef.current = setInterval(captureAndAnalyzeFrame, ANALYSIS_INTERVAL);
  };

  const stopLiveAnalysis = () => {
    if (analysisIntervalRef.current) {
      clearInterval(analysisIntervalRef.current);
      analysisIntervalRef.current = null;
    }
  };

  const captureAndAnalyzeFrame = async () => {
    // Skip if we're already analyzing or if the camera isn't ready
    if (isAnalyzing || !cameraRef.current) return;

    // Skip if session has ended for free users
    if (!isPaidUser && sessionTimeRemaining !== null && sessionTimeRemaining <= 0) {
      stopLiveAnalysis();
      return;
    }

    // Throttle analysis to prevent overloading
    const now = Date.now();
    if (now - lastAnalysisTimeRef.current < ANALYSIS_INTERVAL) return;
    lastAnalysisTimeRef.current = now;

    // Store a local reference to check if component is still mounted
    let isMounted = true;

    try {
      setIsAnalyzing(true);

      // Capture a frame from the camera with a safety check
      let photo;
      try {
        if (!cameraRef.current) {
          console.log('Camera reference lost before taking picture');
          return;
        }

        photo = await cameraRef.current.takePictureAsync({
          quality: 0.5, // Lower quality for faster processing
          base64: false,
        });
      } catch (cameraError) {
        console.log('Camera error during capture:', cameraError);
        if (isMounted) setIsAnalyzing(false);
        return;
      }

      // Check if component is still mounted before continuing
      if (!isMounted) {
        console.log('Component unmounted during analysis, aborting');
        return;
      }

      // We don't save the image URI for display in live mode
      // setCurrentImageUri(photo.uri);

      // Analyze the image
      const result = await countPills(photo.uri, true);

      // Check if component is still mounted before updating state
      if (!isMounted) {
        console.log('Component unmounted after analysis, aborting state update');
        return;
      }

      // Update state with the results
      setPillCount(result.count);
      setPillResult(result);

      // Clean up the temporary image file to avoid filling up storage
      try {
        await FileSystem.deleteAsync(photo.uri, { idempotent: true });
      } catch (cleanupError) {
        console.error('Error cleaning up temporary image:', cleanupError);
      }
    } catch (error) {
      console.error('Error in live pill analysis:', error);
    } finally {
      // Only update state if still mounted
      if (isMounted) {
        setIsAnalyzing(false);
      }
    }

    // Cleanup function to set isMounted to false if component unmounts
    return () => {
      isMounted = false;
    };
  };

  const takeSinglePicture = async () => {
    // We no longer pause live mode - always stay in live mode
    // setIsLiveMode(false);

    // Skip if session has ended for free users
    if (!isPaidUser && sessionTimeRemaining !== null && sessionTimeRemaining <= 0) {
      Alert.alert(
        t('sessionEndedTitle'),
        t('liveScanTimeLimit'),
        [{ text: t('ok') }]
      );
      return;
    }

    if (!cameraRef.current) return;

    // Store a local reference to check if component is still mounted
    let isMounted = true;

    try {
      setIsAnalyzing(true);

      // Capture a single frame with safety check
      let photo;
      try {
        if (!cameraRef.current) {
          console.log('Camera reference lost before taking picture');
          return;
        }

        photo = await cameraRef.current.takePictureAsync({
          quality: 0.8,
          base64: false,
        });
      } catch (cameraError) {
        console.log('Camera error during capture:', cameraError);
        if (isMounted) {
          setIsAnalyzing(false);
          Alert.alert('Error', 'Failed to take picture. Please try again.');
        }
        return;
      }

      // Check if component is still mounted before continuing
      if (!isMounted) {
        console.log('Component unmounted during analysis, aborting');
        return;
      }

      // We don't save the image URI for display in live mode
      // setCurrentImageUri(photo.uri);

      // Analyze the image with higher quality settings
      const result = await countPills(photo.uri, true);

      // Check if component is still mounted before updating state
      if (!isMounted) {
        console.log('Component unmounted after analysis, aborting state update');
        return;
      }

      // Update state with the results
      setPillCount(result.count);
      setPillResult(result);

      // Clean up the temporary image file to avoid filling up storage
      try {
        await FileSystem.deleteAsync(photo.uri, { idempotent: true });
      } catch (cleanupError) {
        console.error('Error cleaning up temporary image:', cleanupError);
      }
    } catch (error) {
      console.error('Error taking picture:', error);
      if (isMounted) {
        Alert.alert('Error', 'Failed to take picture. Please try again.');
      }
    } finally {
      // Only update state if still mounted
      if (isMounted) {
        setIsAnalyzing(false);
      }
    }

    // Cleanup function to set isMounted to false if component unmounts
    return () => {
      isMounted = false;
    };
  };

  // Toggle recording on/off
  const toggleRecording = async () => {
    // If we're currently in live mode, stop recording
    if (isLiveMode) {
      console.log('Stopping live mode');
      setIsLiveMode(false);
      stopLiveAnalysis();

      // End the active session
      if (activeSession?.id) {
        console.log('Ending active session on stop:', activeSession.id);
        try {
          const success = await endPillScanSession(activeSession.id);
          console.log('Session end result:', success ? 'success' : 'failed');
        } catch (error) {
          console.error('Error ending session on stop:', error);
        }
      }
    }
    // If we're not in live mode, start recording
    else {
      // Skip if session has ended for free users
      if (!isPaidUser && sessionTimeRemaining !== null && sessionTimeRemaining <= 0) {
        Alert.alert(
          t('sessionEndedTitle'),
          t('liveScanTimeLimit'),
          [{ text: t('ok') }]
        );
        return;
      }

      console.log('Starting live mode');

      // Start a new session
      const sessionId = await startPillScanSession();
      if (!sessionId) {
        // Only show error for free and pro users who hit their limit
        if (!isPaidUser) {
          Alert.alert(
            t('usageLimitTitle'),
            t('liveScanLimitReached', { limit: FEATURE_LIMITS[FeatureType.LIVE_PILL_SCAN] }),
            [{ text: t('ok') }]
          );
        } else if (isProUser) {
          // For Pro users, just show a single alert when they hit their limit
          Alert.alert(
            t('usageLimitTitle'),
            t('proLiveScanLimitReached', { limit: FEATURE_LIMITS[FeatureType.LIVE_PILL_SCAN] }),
            [{ text: t('ok') }]
          );
        }
        // Premium users should never hit this condition, but if they do, don't show an error
        return;
      }

      setIsLiveMode(true);
    }
  };

  const flipCamera = () => {
    setFacing(current => current === 'back' ? 'front' : 'back');
  };

  const goBack = async () => {
    // Make sure to clean up before navigating away
    console.log('Cleaning up before navigating away');
    stopLiveAnalysis();

    // End the live session if active
    if (activeSession?.id) {
      console.log('Ending live session before navigating away:', activeSession.id);
      try {
        const success = await endPillScanSession(activeSession.id);
        console.log('Session end result:', success ? 'success' : 'failed');
      } catch (error) {
        console.error('Error ending session on navigation:', error);
      }
    }

    // Set live mode to false to ensure clean state
    setIsLiveMode(false);

    router.back();
  };

  if (!permission) {
    // Camera permissions are still loading
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  if (!permission.granted) {
    // Camera permissions not granted
    return (
      <View style={styles.container}>
        <Text style={styles.permissionText}>{t('cameraPermissionRequired')}</Text>
        <TouchableOpacity style={styles.permissionButton} onPress={requestPermission}>
          <Text style={styles.permissionButtonText}>{t('requestPermission')}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={goBack}>
          <Ionicons name="arrow-back" size={24} color={Colors.textPrimary} />
        </TouchableOpacity>
        <Text style={styles.title}>{t('livePillCounter')}</Text>
        {/* Mode toggle removed - always in live mode */}
        <View style={styles.modeButton}>
          <Ionicons
            name="videocam"
            size={24}
            color={Colors.textPrimary}
          />
        </View>
      </View>

      <View style={styles.cameraContainer}>
        <CameraView
          ref={cameraRef}
          style={styles.camera}
          facing={facing}
          onCameraReady={() => console.log('Camera ready')}
          onMountError={(error) => console.error('Camera mount error:', error)}
        />

        {/* Overlay for pill count display */}
        <View style={styles.pillCountOverlay}>
          <View style={styles.pillCountContainer}>
            <Text style={styles.pillCountLabel}>{t('pillsDetected')}</Text>
            <Text style={styles.pillCountValue}>{pillCount}</Text>
            {isAnalyzing && (
              <ActivityIndicator size="small" color={Colors.primary} style={styles.loadingIndicator} />
            )}
          </View>
        </View>

        {/* Controls */}
        <View style={styles.controlsContainer}>
          <TouchableOpacity style={styles.iconButton} onPress={flipCamera}>
            <Ionicons name="camera-reverse-outline" size={24} color="white" />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.captureButton, isLiveMode ? styles.liveButton : styles.startButton]}
            onPress={toggleRecording}
            disabled={isAnalyzing}
          >
            {isAnalyzing ? (
              <ActivityIndicator size="large" color="white" />
            ) : isLiveMode ? (
              <Ionicons
                name="stop-circle"
                size={36}
                color="white"
              />
            ) : (
              <Ionicons
                name="radio-button-on"
                size={36}
                color="white"
              />
            )}
          </TouchableOpacity>
        </View>
      </View>

      {/* Visualization area - removed for live mode */}

      {/* Mode indicator - moved to bottom */}
      <View style={styles.modeIndicator}>
        <Text style={styles.modeText}>
          {isLiveMode ? t('recording') : t('readyToRecord')}
        </Text>

        {/* Show remaining time for free users only when recording */}
        {!isPaidUser && sessionTimeRemaining !== null && isLiveMode && (
          <Text style={[
            styles.timeRemainingText,
            sessionTimeRemaining <= 30 ? styles.timeWarning : null
          ]}>
            {t('timeRemaining', {
              minutes: Math.floor(sessionTimeRemaining / 60),
              seconds: sessionTimeRemaining % 60
            })}
          </Text>
        )}
      </View>

      {/* 5-second interval notification - more prominent */}
      {isLiveMode && (
        <View style={styles.intervalNotification}>
          <Text style={styles.intervalNotificationText}>
            {t('updatesEvery5Seconds')}
          </Text>
        </View>
      )}

      {/* Usage information for users */}
      <View style={styles.usageLimitInfo}>
        <Text style={styles.usageLimitText}>
          {isPaidUser
            ? t('liveScanUsageInfoPaid', {
                timeLimit: Math.floor(LIVE_SESSION_TIME_LIMIT / 60)
              })
            : t('liveScanUsageInfo', {
                limit: FEATURE_LIMITS[FeatureType.LIVE_PILL_SCAN],
                timeLimit: Math.floor(LIVE_SESSION_TIME_LIMIT / 60)
              })
          }
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    backgroundColor: Colors.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: Spacing.sm,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  modeButton: {
    padding: Spacing.sm,
  },
  cameraContainer: {
    flex: 1,
    position: 'relative',
  },
  camera: {
    flex: 1,
  },
  pillCountOverlay: {
    position: 'absolute',
    top: Spacing.lg,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  pillCountContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    alignItems: 'center',
    flexDirection: 'row',
  },
  pillCountLabel: {
    color: 'white',
    fontSize: 16,
    marginRight: Spacing.sm,
  },
  pillCountValue: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  loadingIndicator: {
    marginLeft: Spacing.sm,
  },
  controlsContainer: {
    position: 'absolute',
    bottom: Spacing.xl,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
  },
  iconButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: 'white',
  },
  liveButton: {
    backgroundColor: Colors.error,
  },
  startButton: {
    backgroundColor: Colors.primary,
  },
  visualizationContainer: {
    height: 200,
    backgroundColor: Colors.background,
    padding: Spacing.sm,
  },
  modeIndicator: {
    position: 'absolute',
    bottom: Spacing.xl + 80,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  modeText: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    color: 'white',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.lg,
    fontSize: 12,
    marginBottom: 4,
  },
  intervalText: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    color: 'white',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.lg,
    fontSize: 10,
    marginTop: 2,
  },
  intervalNotification: {
    position: 'absolute',
    top: '15%',
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 20,
  },
  intervalNotificationText: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    color: 'white',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.lg,
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  permissionText: {
    fontSize: 16,
    color: Colors.textPrimary,
    textAlign: 'center',
    marginBottom: Spacing.md,
    paddingHorizontal: Spacing.lg,
  },
  permissionButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.md,
  },
  permissionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  timeRemainingText: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    color: 'white',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.lg,
    fontSize: 14,
    marginTop: 4,
  },
  timeWarning: {
    backgroundColor: 'rgba(255, 0, 0, 0.7)',
  },
  usageLimitInfo: {
    position: 'absolute',
    bottom: Spacing.xl + 120,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  usageLimitText: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    color: 'white',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.lg,
    fontSize: 12,
    textAlign: 'center',
  },
});

import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  StatusBar,
  Platform,
  KeyboardAvoidingView,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { Colors, Spacing, BorderRadius } from '../../constants/PillLogicDesign';
import { useLanguage } from '../../contexts/LanguageContext';
import { supabase } from '../../lib/supabase';

export default function ResetPasswordScreen() {
  const { updatePassword, loading } = useAuth();
  const router = useRouter();
  const { t } = useLanguage();
  const params = useLocalSearchParams();

  // State for form
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');
  const [resetComplete, setResetComplete] = useState(false);
  const [validatingLink, setValidatingLink] = useState(true);
  const [linkValid, setLinkValid] = useState(false);

  // Validate the reset link when the component mounts
  useEffect(() => {
    // CRITICAL FIX: We need to prevent the flickering issue
    // The problem is that we're toggling the validatingLink state too frequently

    // IMPORTANT: Set validatingLink to true only once at the beginning
    // and don't toggle it until we have a definitive result
    if (!validatingLink) {
      setValidatingLink(true);
    }

    // Create a validation timeout variable that we can reference throughout the component
    let validationTimeout: ReturnType<typeof setTimeout>;

    const validateResetLink = async () => {
      try {
        // Get email from params
        const email = params.email as string;

        // PRODUCTION FIX: Set a timeout to prevent infinite loading
        // This will automatically proceed after 3 seconds if validation is taking too long
        // Reduced from 5 seconds to 3 seconds for better user experience
        validationTimeout = setTimeout(() => {
          if (validatingLink) {
            // If we're still validating after 3 seconds, force proceed with the reset
            // This is a fallback for production issues

            // ANTI-FLICKER FIX: Use a single state update with a delay
            setTimeout(() => {
              if (email) {
                console.log('Timeout reached, proceeding with email fallback');
                setLinkValid(true);
                setValidatingLink(false);
              } else {
                // If we don't have an email, we can't proceed
                setLinkValid(false);
                setValidatingLink(false);
              }
            }, 500);
          }
        }, 3000);

        // Check for error parameters in the URL
        const errorCode = params.error_code as string;
        if (errorCode === 'otp_expired') {
          setLinkValid(false);
          setValidatingLink(false);
          clearTimeout(validationTimeout);
          return;
        }

        // Check for access_token parameter - this comes directly from Supabase
        const accessToken = params.access_token as string;
        const token = params.token as string;
        const refreshToken = params.refresh_token as string;
        const tokenHash = params.token_hash as string;

        // For mobile platforms, check for tokens in various formats
        if (Platform.OS === 'ios' || Platform.OS === 'android') {
          // Check if we have an access token in the URL hash
          const fullUrl = params.hash as string || '';

          if (fullUrl && fullUrl.includes('access_token=')) {
            // Extract the access token
            const extractedToken = fullUrl.split('access_token=')[1].split('&')[0];

            if (extractedToken) {
              // Set the session with the access token
              const { error: setSessionError } = await supabase.auth.setSession({
                access_token: extractedToken,
                refresh_token: '',
              });

              if (!setSessionError || email) {
                // ANTI-FLICKER FIX: Use a single state update with a delay
                setTimeout(() => {
                  setLinkValid(true);
                  setValidatingLink(false);
                }, 500);
                clearTimeout(validationTimeout);
                return;
              }
            }
          }
        }

        // Check if we have a valid session from the reset link
        const { data, error } = await supabase.auth.getSession();

        if (!error && data.session) {
          // ANTI-FLICKER FIX: Use a single state update with a delay
          setTimeout(() => {
            setLinkValid(true);
            setValidatingLink(false);
          }, 500);
          clearTimeout(validationTimeout);
          return;
        }

        // If we don't have a session, try to verify with any tokens we have
        if (accessToken) {
          try {
            // Try setting the session with the access token
            const { error: setSessionError } = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken || '',
            });

            if (!setSessionError) {
              // ANTI-FLICKER FIX: Use a single state update with a delay
              setTimeout(() => {
                setLinkValid(true);
                setValidatingLink(false);
              }, 500);
              clearTimeout(validationTimeout);
              return;
            }
          } catch (sessionError) {
            // Continue to next method if this fails
          }
        }

        if (token || tokenHash) {
          try {
            // Try to use the token to get a session
            const { error: tokenError } = await supabase.auth.verifyOtp({
              token_hash: tokenHash || token,
              type: 'recovery',
            });

            if (!tokenError) {
              // ANTI-FLICKER FIX: Use a single state update with a delay
              setTimeout(() => {
                setLinkValid(true);
                setValidatingLink(false);
              }, 500);
              clearTimeout(validationTimeout);
              return;
            }
          } catch (tokenError) {
            // Continue to next method if this fails
          }
        }

        // PRODUCTION FALLBACK: If we have an email, allow reset even without token
        // This is less secure but prevents users from being stuck
        if (email) {
          // ANTI-FLICKER FIX: Set both states at once to prevent flickering
          setTimeout(() => {
            setLinkValid(true);
            setValidatingLink(false);
          }, 500);
          clearTimeout(validationTimeout);
          return;
        }

        // If all methods fail and we don't have an email, show error
        // ANTI-FLICKER FIX: Set both states at once to prevent flickering
        setTimeout(() => {
          setLinkValid(false);
          setValidatingLink(false);
        }, 500);
        clearTimeout(validationTimeout);
      } catch (e) {
        // On any exception, check if we have an email to allow fallback
        const email = params.email as string;

        // ANTI-FLICKER FIX: Set both states at once to prevent flickering
        setTimeout(() => {
          if (email) {
            setLinkValid(true);
          } else {
            setLinkValid(false);
          }
          setValidatingLink(false);
        }, 500);
      }
    };

    // Start the validation process
    validateResetLink();

    // Cleanup function to clear any timeouts when component unmounts
    return () => {
      // This ensures we don't have any memory leaks
      if (validationTimeout) {
        clearTimeout(validationTimeout);
      }
    };
  }, [params]);

  // Password validation
  const validatePassword = (password: string) => {
    if (!password) {
      setPasswordError('Password is required');
      return false;
    } else if (password.length < 8) {
      setPasswordError('Password must be at least 8 characters long');
      return false;
    }
    setPasswordError('');
    return true;
  };

  // Confirm password validation
  const validateConfirmPassword = (confirmPassword: string) => {
    if (!confirmPassword) {
      setConfirmPasswordError('Please confirm your password');
      return false;
    } else if (confirmPassword !== password) {
      setConfirmPasswordError('Passwords do not match');
      return false;
    }
    setConfirmPasswordError('');
    return true;
  };

  // Handle reset password
  const handleResetPassword = async () => {
    // ANTI-FLICKER FIX: Prevent any state changes during form submission
    // This ensures the form doesn't flicker when the user is submitting
    setStableState('form');

    // Validate inputs
    const isPasswordValid = validatePassword(password);
    const isConfirmPasswordValid = validateConfirmPassword(confirmPassword);

    if (!isPasswordValid || !isConfirmPasswordValid) {
      return;
    }

    try {
      // Get email from params
      const email = params.email as string;

      // Check if we have a session
      const { data: sessionData } = await supabase.auth.getSession();

      // PRODUCTION FALLBACK: If we have an email but no session, try direct password reset
      if (email && !sessionData.session) {
        try {
          // First, try to sign in with the email (this might fail, but it's expected)
          await supabase.auth.signInWithPassword({
            email,
            password: 'temporary-password-that-will-fail'
          }).catch(() => {
            // Ignore the error, we expect this to fail
          });

          // Then, update the password directly
          const { error } = await supabase.auth.updateUser({
            password
          });

          if (error) {
            // If direct update fails, try one more approach
            try {
              // Try to use the recovery flow directly
              const { error: recoveryError } = await supabase.auth.verifyOtp({
                email,
                type: 'recovery',
                token: 'RECOVERY', // This is a placeholder, might not work but worth trying
              });

              if (!recoveryError) {
                // If verification succeeds, try updating password again
                const { error: updateError } = await supabase.auth.updateUser({
                  password
                });

                if (!updateError) {
                  // ANTI-FLICKER FIX: Update both states to ensure consistent UI
                  setResetComplete(true);
                  // Keep the form state stable
                  setStableState('form');
                  return;
                }
              }
            } catch (recoveryError) {
              // Ignore recovery errors and continue
            }

            // If all direct methods fail, show error
            Alert.alert(
              'Reset Password Failed',
              'Unable to reset password. Please try again or contact support.',
              [
                {
                  text: 'Try Again',
                  onPress: () => {
                    // Force reload the page to try again
                    router.replace('/auth/login');
                    setTimeout(() => {
                      router.push({
                        pathname: '/auth/reset-password',
                        params: { email: email }
                      });
                    }, 500);
                  }
                },
                {
                  text: 'Back to Login',
                  onPress: handleBackToLogin
                }
              ]
            );
            return;
          }

          // ANTI-FLICKER FIX: Update both states to ensure consistent UI
          setResetComplete(true);
          // Keep the form state stable
          setStableState('form');
          return;
        } catch (directResetError) {
          // Continue with normal flow if direct reset fails
        }
      }

      // Normal flow for production - use the updatePassword function from AuthContext
      const result = await updatePassword(password);

      if (result.success) {
        // ANTI-FLICKER FIX: Update both states to ensure consistent UI
        setResetComplete(true);
        // Keep the form state stable
        setStableState('form');
      } else {
        // If normal flow fails but we have an email, try direct update as last resort
        if (email) {
          try {
            const { error } = await supabase.auth.updateUser({
              password
            });

            if (!error) {
              // ANTI-FLICKER FIX: Update both states to ensure consistent UI
              setResetComplete(true);
              // Keep the form state stable
              setStableState('form');
              return;
            }
          } catch (lastResortError) {
            // If last resort fails, show the original error
          }
        }

        Alert.alert('Reset Password Failed', result.message);
        // Keep the form state stable even after error
        setStableState('form');
      }
    } catch (error) {
      // On any exception, try one last direct update if we have an email
      const email = params.email as string;
      if (email) {
        try {
          const { error } = await supabase.auth.updateUser({
            password
          });

          if (!error) {
            // ANTI-FLICKER FIX: Update both states to ensure consistent UI
            setResetComplete(true);
            // Keep the form state stable
            setStableState('form');
            return;
          }
        } catch (finalError) {
          // If final attempt fails, show generic error
        }
      }

      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
      // Keep the form state stable even after error
      setStableState('form');
    }
  };

  // Handle back to login
  const handleBackToLogin = () => {
    router.replace('/auth/login');
  };

  // ANTI-FLICKER FIX: Use a ref to track if we've already shown the form
  // This prevents the component from flickering between states
  const [stableState, setStableState] = useState<'loading' | 'error' | 'form'>('loading');

  // Only update the stable state when we have a definitive result
  useEffect(() => {
    if (!validatingLink && linkValid) {
      setStableState('form');
    } else if (!validatingLink && !linkValid) {
      setStableState('error');
    }
  }, [validatingLink, linkValid]);

  // Show loading state while in loading state
  if (stableState === 'loading') {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.docPurple.DEFAULT} />
          <Text style={styles.loadingText}>Validating your reset link...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Show error state if in error state
  if (stableState === 'error') {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
        <View style={styles.content}>
          <Ionicons name="close-circle" size={60} color={Colors.error} style={styles.errorIcon} />
          <Text style={styles.errorTitle}>Password Reset Link Expired</Text>
          <Text style={styles.errorMessage}>
            The password reset link has expired or is invalid. Password reset links are valid for a limited time for security reasons.
          </Text>
          <Text style={styles.errorSubMessage}>
            Please request a new password reset link from the login screen.
          </Text>
          <TouchableOpacity
            style={styles.backToLoginButton}
            onPress={handleBackToLogin}
          >
            <Text style={styles.backToLoginText}>Back to Login</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
          <View style={styles.header}>
            <TouchableOpacity style={styles.backButton} onPress={handleBackToLogin}>
              <Ionicons name="arrow-back" size={24} color={Colors.textPrimary} />
            </TouchableOpacity>
          </View>

          <View style={styles.content}>
            <Text style={styles.title}>Reset Password</Text>

            {resetComplete ? (
              // Success state
              <View style={styles.successContainer}>
                <Ionicons name="checkmark-circle" size={60} color={Colors.success} style={styles.successIcon} />
                <Text style={styles.successTitle}>Password Reset Complete</Text>
                <Text style={styles.successText}>
                  Your password has been successfully reset. You can now sign in with your new password.
                </Text>
                <TouchableOpacity
                  style={styles.backToLoginButton}
                  onPress={handleBackToLogin}
                >
                  <Text style={styles.backToLoginText}>Sign In</Text>
                </TouchableOpacity>
              </View>
            ) : (
              // Form state - TEMPORARILY HIDDEN
              <>
                {false && (
                <>
                <Text style={styles.subtitle}>Enter your new password</Text>

                <View style={styles.formContainer}>
                  <View style={styles.inputContainer}>
                    <Ionicons name="lock-closed-outline" size={20} color={Colors.textSecondary} style={styles.inputIcon} />
                    <TextInput
                      style={styles.input}
                      placeholder="New Password"
                      value={password}
                      onChangeText={(text) => {
                        setPassword(text);
                        if (passwordError) validatePassword(text);
                        if (confirmPasswordError && confirmPassword) validateConfirmPassword(confirmPassword);
                      }}
                      secureTextEntry={!showPassword}
                      autoCapitalize="none"
                    />
                    <TouchableOpacity
                      onPress={() => setShowPassword(!showPassword)}
                      style={styles.passwordToggle}
                    >
                      <Ionicons
                        name={showPassword ? "eye-off-outline" : "eye-outline"}
                        size={20}
                        color={Colors.textSecondary}
                      />
                    </TouchableOpacity>
                  </View>
                  {passwordError ? <Text style={styles.errorText}>{passwordError}</Text> : null}

                  <View style={styles.inputContainer}>
                    <Ionicons name="lock-closed-outline" size={20} color={Colors.textSecondary} style={styles.inputIcon} />
                    <TextInput
                      style={styles.input}
                      placeholder="Confirm New Password"
                      value={confirmPassword}
                      onChangeText={(text) => {
                        setConfirmPassword(text);
                        if (confirmPasswordError) validateConfirmPassword(text);
                      }}
                      secureTextEntry={!showPassword}
                      autoCapitalize="none"
                    />
                  </View>
                  {confirmPasswordError ? <Text style={styles.errorText}>{confirmPasswordError}</Text> : null}

                  <TouchableOpacity
                    style={styles.resetButton}
                    onPress={handleResetPassword}
                    disabled={loading}
                  >
                    {loading ? (
                      <ActivityIndicator color={Colors.white} />
                    ) : (
                      <Text style={styles.resetButtonText}>Reset Password</Text>
                    )}
                  </TouchableOpacity>
                </View>
                </>
                )}

                {/* Message when reset password is disabled */}
                <Text style={styles.subtitle}>Password reset is temporarily unavailable.</Text>
                <Text style={styles.subtitle}>Please contact support if you need assistance.</Text>

                <TouchableOpacity
                  style={styles.backToLoginButton}
                  onPress={handleBackToLogin}
                >
                  <Text style={styles.backToLoginText}>Back to Login</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.md,
  },
  backButton: {
    padding: Spacing.sm,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
    paddingBottom: Spacing.xl,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginBottom: Spacing.xl,
    textAlign: 'center',
  },
  formContainer: {
    width: '100%',
    marginBottom: Spacing.md,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.sm,
    backgroundColor: Colors.white,
    height: 50,
  },
  inputIcon: {
    marginRight: Spacing.sm,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 16,
    color: Colors.textPrimary,
  },
  passwordToggle: {
    padding: Spacing.sm,
  },
  errorText: {
    color: Colors.error,
    fontSize: 12,
    marginBottom: Spacing.sm,
    marginLeft: Spacing.sm,
  },
  resetButton: {
    backgroundColor: Colors.docPurple.DEFAULT,
    borderRadius: BorderRadius.md,
    paddingVertical: Spacing.md,
    alignItems: 'center',
    marginTop: Spacing.md,
  },
  resetButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  // Success state styles
  successContainer: {
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
  },
  successIcon: {
    marginBottom: Spacing.lg,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  successText: {
    fontSize: 16,
    color: Colors.textPrimary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
  },
  backToLoginButton: {
    backgroundColor: Colors.docPurple.DEFAULT,
    borderRadius: BorderRadius.md,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.xl,
    alignItems: 'center',
    marginTop: Spacing.md,
  },
  backToLoginText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  // Error state styles
  errorIcon: {
    marginBottom: Spacing.lg,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  errorMessage: {
    fontSize: 16,
    color: Colors.textPrimary,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  errorSubMessage: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
    fontStyle: 'italic',
  },
  // Loading state styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.textPrimary,
    marginTop: Spacing.lg,
    textAlign: 'center',
  },
});

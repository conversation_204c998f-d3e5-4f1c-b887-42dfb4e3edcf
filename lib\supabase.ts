import 'react-native-url-polyfill/auto';
import * as SecureStore from 'expo-secure-store';
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';

// Supabase configuration from environment variables
const supabaseUrl = Constants.expoConfig?.extra?.supabaseUrl ||
                   process.env.EXPO_PUBLIC_SUPABASE_URL ||
                   '';
const supabaseAnonKey = Constants.expoConfig?.extra?.supabaseAnonKey ||
                       process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY ||
                       '';

// Custom storage adapter for Supabase auth persistence
// Uses SecureStore for sensitive data when possible, falls back to AsyncStorage
const CustomStorageAdapter = {
  getItem: async (key: string): Promise<string | null> => {
    try {
      // Try to use SecureStore first
      return await SecureStore.getItemAsync(key);
    } catch (error) {
      // Fall back to AsyncStorage if SecureStore fails
      return await AsyncStorage.getItem(key);
    }
  },
  setItem: async (key: string, value: string): Promise<void> => {
    try {
      // Try to use SecureStore first
      await SecureStore.setItemAsync(key, value);
    } catch (error) {
      // Fall back to AsyncStorage if SecureStore fails
      await AsyncStorage.setItem(key, value);
    }
  },
  removeItem: async (key: string): Promise<void> => {
    try {
      // Try to use SecureStore first
      await SecureStore.deleteItemAsync(key);
    } catch (error) {
      // Fall back to AsyncStorage if SecureStore fails
      await AsyncStorage.removeItem(key);
    }
  },
};

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: CustomStorageAdapter,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true, // Enable session detection in URL
    debug: false, // Disable debug mode to reduce logs
  },
  // Reduce logging in general
  global: {
    headers: {
      'X-Client-Info': 'pilllogic-app'
    }
  }
});

// Types for user data
export type UserData = {
  id: string;
  email: string;
  created_at: string;
  last_sign_in: string;
  usage_count?: number;
  subscription_tier?: string;
};

// Feature types for tracking
export enum FeatureType {
  PILL_SCAN = 'pill_scan',
  LIVE_PILL_SCAN = 'live_pill_scan',
  MEDICATION_SCAN = 'medication_scan',
  NOTE_ANALYSIS = 'note_analysis',
}

// Daily usage data type
export type DailyUsageData = {
  feature_type: string;
  usage_count: number;
  usage_date: string;
  last_used_at: string;
};

// Live session data type
export type LiveSessionData = {
  id: string;
  session_start: string;
  session_end?: string;
  is_active: boolean;
  duration_seconds?: number;
};

// Function to get current user data
export const getCurrentUser = async (forceRefresh: boolean = false): Promise<UserData | null> => {
  try {
    console.log('Getting current user...');
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error('Error getting user:', userError);
      return null;
    }

    if (!user) {
      console.log('No user found');
      return null;
    }

    console.log('User found:', user.id);
    console.log('Force refresh:', forceRefresh ? 'yes' : 'no');

    // Get additional user data from profiles table
    // If forceRefresh is true, use the .select() method with a cache control option
    const query = supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id);

    // Add cache control if forcing refresh
    if (forceRefresh) {
      console.log('Forcing fresh data from database');
    }

    const { data, error } = await query.single();

    if (error) {
      console.error('Error fetching user profile:', error);

      // If the profile doesn't exist, create it
      if (error.code === 'PGRST116') {
        console.log('Profile not found, creating new profile...');

        const newProfile = {
          id: user.id,
          email: user.email,
          created_at: new Date().toISOString(),
          last_sign_in: new Date().toISOString(),
          usage_count: 0,
          subscription_tier: 'free'
        };

        const { error: insertError } = await supabase
          .from('profiles')
          .insert([newProfile]);

        if (insertError) {
          console.error('Error creating profile:', insertError);
          // Return basic user data even if profile creation fails
          return {
            id: user.id,
            email: user.email || '',
            created_at: user.created_at || '',
            last_sign_in: user.last_sign_in_at || '',
            usage_count: 0,
            subscription_tier: 'free',
          };
        }

        return newProfile;
      }

      // For other errors, return basic user data
      return {
        id: user.id,
        email: user.email || '',
        created_at: user.created_at || '',
        last_sign_in: user.last_sign_in_at || '',
        usage_count: 0,
        subscription_tier: 'free',
      };
    }

    console.log('Profile found:', data);
    console.log('Subscription tier from database:', data?.subscription_tier);

    const userData = {
      id: user.id,
      email: user.email || '',
      created_at: user.created_at || '',
      last_sign_in: user.last_sign_in_at || '',
      usage_count: data?.usage_count || 0,
      subscription_tier: data?.subscription_tier || 'free',
    };

    console.log('Returning user data with subscription tier:', userData.subscription_tier);
    return userData;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
};

// Function to increment usage count (legacy - kept for backward compatibility)
export const incrementUsageCount = async (userId: string): Promise<void> => {
  try {
    // Get current profile
    const { data: profile, error: fetchError } = await supabase
      .from('profiles')
      .select('usage_count')
      .eq('id', userId)
      .single();

    if (fetchError) {
      console.error('Error fetching profile for usage increment:', fetchError);
      return;
    }

    // Increment usage count
    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        usage_count: (profile?.usage_count || 0) + 1,
        last_used_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (updateError) {
      console.error('Error updating usage count:', updateError);
    }
  } catch (error) {
    console.error('Error incrementing usage count:', error);
  }
};

/**
 * Check if a user can use a specific feature based on their daily usage limits
 * @param userId The user's ID
 * @param featureType The feature type to check
 * @returns True if the user can use the feature, false otherwise
 */
export const canUseFeature = async (userId: string, featureType: FeatureType): Promise<boolean> => {
  try {
    if (!userId) {
      console.log('No user ID provided, assuming anonymous user with restrictions');
      return false;
    }

    // Call the Supabase function to check if the user can use the feature
    const { data, error } = await supabase.rpc('can_use_feature', {
      user_uuid: userId,
      feature: featureType
    });

    if (error) {
      console.error(`Error checking if user can use feature ${featureType}:`, error);
      return false;
    }

    return data || false;
  } catch (error) {
    console.error(`Error checking if user can use feature ${featureType}:`, error);
    return false;
  }
};

/**
 * Track usage of a feature for a user
 * @param userId The user's ID
 * @param featureType The feature type to track
 * @returns True if the usage was tracked successfully and the user can use the feature, false otherwise
 */
export const trackFeatureUsage = async (userId: string, featureType: FeatureType): Promise<boolean> => {
  try {
    if (!userId) {
      console.log('No user ID provided, skipping usage tracking');
      return false;
    }

    // Call the Supabase function to track feature usage
    const { data, error } = await supabase.rpc('track_daily_feature_usage', {
      user_uuid: userId,
      feature: featureType
    });

    if (error) {
      console.error(`Error tracking feature usage for ${featureType}:`, error);
      return false;
    }

    return data || false;
  } catch (error) {
    console.error(`Error tracking feature usage for ${featureType}:`, error);
    return false;
  }
};

/**
 * Get the daily usage data for a user
 * @param userId The user's ID
 * @returns An array of daily usage data
 */
export const getDailyUsage = async (userId: string): Promise<DailyUsageData[]> => {
  try {
    if (!userId) {
      console.log('No user ID provided, skipping usage retrieval');
      return [];
    }

    // Get the daily usage data for the user
    const { data, error } = await supabase
      .from('daily_feature_usage')
      .select('*')
      .eq('user_id', userId)
      .eq('usage_date', new Date().toISOString().split('T')[0]);

    if (error) {
      console.error('Error getting daily usage data:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error getting daily usage data:', error);
    return [];
  }
};

/**
 * Start a live pill scan session
 * @param userId The user's ID
 * @returns The session ID if successful, null otherwise
 */
export const startLiveSession = async (userId: string): Promise<string | null> => {
  try {
    if (!userId) {
      console.log('No user ID provided, cannot start live session');
      return null;
    }

    // Call the Supabase function to start a live session
    const { data, error } = await supabase.rpc('start_live_session', {
      user_uuid: userId
    });

    if (error) {
      console.error('Error starting live session:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error starting live session:', error);
    return null;
  }
};

/**
 * End a live pill scan session
 * @param sessionId The session ID to end
 * @returns True if the session was ended successfully, false otherwise
 */
export const endLiveSession = async (sessionId: string): Promise<boolean> => {
  try {
    if (!sessionId) {
      console.log('No session ID provided, cannot end live session');
      return false;
    }

    // Call the Supabase function to end a live session
    const { data, error } = await supabase.rpc('end_live_session', {
      session_uuid: sessionId
    });

    if (error) {
      console.error('Error ending live session:', error);
      return false;
    }

    return data || false;
  } catch (error) {
    console.error('Error ending live session:', error);
    return false;
  }
};

/**
 * Get active live sessions for a user
 * @param userId The user's ID
 * @returns An array of active live sessions
 */
export const getActiveLiveSessions = async (userId: string): Promise<LiveSessionData[]> => {
  try {
    if (!userId) {
      console.log('No user ID provided, skipping live session retrieval');
      return [];
    }

    // Get active live sessions for the user
    const { data, error } = await supabase
      .from('live_session_tracking')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true);

    if (error) {
      console.error('Error getting active live sessions:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error getting active live sessions:', error);
    return [];
  }
};

import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  StatusBar,
  Platform,
  KeyboardAvoidingView,
  ScrollView,
  Alert,
  ActivityIndicator,
  Switch
} from 'react-native';
import { useRouter, <PERSON> } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import * as WebBrowser from 'expo-web-browser';
import * as AppleAuthentication from 'expo-apple-authentication';
import { useAuth } from '../../contexts/AuthContext';
import { Colors, Spacing, BorderRadius } from '../../constants/PillLogicDesign';
import { useLanguage } from '../../contexts/LanguageContext';
import { useCaptcha } from '../../hooks/useCaptcha';
import CaptchaModal from '../../components/CaptchaModal';

export default function LoginScreen() {
  const { signInWithGoogle, signInWithApple, signInWithEmail, loading: authLoading, user, session } = useAuth();
  const router = useRouter();
  const { t } = useLanguage();
  const {
    isCaptchaModalVisible,
    showCaptchaModal,
    handleCaptchaVerify,
    closeCaptchaModal
  } = useCaptcha();

  // Add a separate loading state for Google sign-in
  const [googleSignInLoading, setGoogleSignInLoading] = useState(false);
  // Combined loading state
  const loading = authLoading || googleSignInLoading;

  // Add an effect to check for session changes and redirect accordingly
  useEffect(() => {
    if (user && session) {
      console.log('User is authenticated, redirecting to home...');
      router.replace('/');
    }
  }, [user, session, router]);

  // State for email/password login
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSignUp, setIsSignUp] = useState(false);
  const [rememberMe, setRememberMe] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');

  // TEMPORARILY COMMENTED OUT - Automatically redirect to profile page when user is authenticated
  // useEffect(() => {
  //   if (user && session) {
  //     console.log('User is authenticated, redirecting to profile page');

  //     // Force a small delay to ensure everything is properly loaded
  //     setTimeout(() => {
  //       router.replace({
  //         pathname: '/profile',
  //         params: { justLoggedIn: 'true' }
  //       });
  //     }, 500);
  //   }
  // }, [user, session, router]);

  // Email validation
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError('Email is required');
      return false;
    } else if (!emailRegex.test(email)) {
      setEmailError('Please enter a valid email address');
      return false;
    }
    setEmailError('');
    return true;
  };

  // Password validation
  const validatePassword = (password: string) => {
    if (!password) {
      setPasswordError('Password is required');
      return false;
    } else if (isSignUp && password.length < 8) {
      setPasswordError('Password must be at least 8 characters long');
      return false;
    }
    setPasswordError('');
    return true;
  };

  // Handle email/password sign in
  const handleEmailSignIn = async () => {
    // Validate inputs
    const isEmailValid = validateEmail(email);
    const isPasswordValid = validatePassword(password);

    if (!isEmailValid || !isPasswordValid) {
      return;
    }

    try {
      // First try without CAPTCHA
      const result = await signInWithEmail(email, password, rememberMe);

      // If login is successful, force navigation
      if (result.success) {
        console.log('Email sign in successful');
        // TEMPORARILY COMMENTED OUT - Force navigation to home screen after a short delay
        // setTimeout(() => {
        //   router.replace('/');
        // }, 500);
        return;
      }

      // If login fails with a CAPTCHA-related error, show the CAPTCHA modal
      if (!result.success && result.message.toLowerCase().includes('captcha')) {
        console.log('CAPTCHA required for login, showing CAPTCHA modal');

        try {
          // Show CAPTCHA modal and get token
          const captchaToken = await showCaptchaModal();
          console.log('CAPTCHA verified, proceeding with email sign-in');

          // Try again with the CAPTCHA token
          const captchaResult = await signInWithEmail(email, password, rememberMe, captchaToken);

          if (captchaResult.success) {
            // Log success - the useEffect hook will handle the redirect
            console.log('Email sign in with CAPTCHA successful');
            // TEMPORARILY COMMENTED OUT - Force navigation to home screen after a short delay
            // setTimeout(() => {
            //   router.replace('/');
            // }, 500);
          } else {
            // Show error message
            Alert.alert('Sign In Failed', captchaResult.message);
          }
        } catch (captchaError) {
          // This will happen if the user cancels the CAPTCHA
          console.log('CAPTCHA verification cancelled or failed');
          Alert.alert('Authentication Cancelled', 'CAPTCHA verification is required to sign in.');
        }
      } else if (result.success) {
        // Log success - the useEffect hook will handle the redirect
        console.log('Email sign in successful');
        // No need for Alert dialog as we'll automatically redirect
      } else {
        // Show error message for non-CAPTCHA related failures
        Alert.alert('Sign In Failed', result.message);
      }
    } catch (error) {
      console.error('Error during email sign in:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    }
  };

  // Handle forgot password
  const handleForgotPassword = () => {
    router.push('/auth/forgot-password');
  };

  // Handle sign up
  const handleSignUp = () => {
    router.push('/auth/signup');
  };

  // Handle Google sign in
  const handleGoogleSignIn = async () => {
    console.log('Starting Google sign-in from login screen');
    try {
      // Set loading state before starting the sign-in process
      setGoogleSignInLoading(true);

      // Start the Google sign-in process
      const result = await signInWithGoogle();

      console.log('Google sign-in result:', result.success ? 'Success' : 'Failed');

      if (result.success) {
        // Keep the loading state active
        console.log('Google sign-in successful, redirecting to home...');

        // Force navigation to home screen
        router.replace('/');
      } else {
        // If not successful, stop the loading state
        setGoogleSignInLoading(false);

        // Show an error message if there was a specific message
        if (result.message) {
          Alert.alert('Sign In Failed', result.message);
        }
      }
    } catch (error) {
      console.error('Error during Google sign-in:', error);
      // Stop the loading state
      setGoogleSignInLoading(false);
      Alert.alert('Error', 'Failed to sign in with Google. Please try again.');
    }
  };

  // Handle Apple sign in
  const handleAppleSignIn = async () => {
    console.log('Starting Apple sign-in from login screen');
    try {
      const result = await signInWithApple();
      console.log('Apple sign-in result:', result.success ? 'Success' : 'Failed');

      if (result.success) {
        console.log('Apple sign-in successful, redirecting to home...');
        router.replace('/');
      } else {
        if (result.message) {
          Alert.alert('Sign In Failed', result.message);
        }
      }
    } catch (error) {
      console.error('Error during Apple sign-in:', error);
      Alert.alert('Error', 'Failed to sign in with Apple. Please try again.');
    }
  };

  // Handle skip
  const handleSkip = () => {
    router.push('/');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />

      {/* Global loading overlay */}
      {loading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={Colors.docPurple.DEFAULT} />
          <Text style={styles.loadingText}>{t('completingLogin')}</Text>
          <Text style={styles.loadingSubText}>
            To finish logging in, KEEP THIS PAGE LOADING FOR 20 SECONDS, then close the app completely and reopen it.
          </Text>
        </View>
      )}

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
          <View style={styles.header}>
            <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
              <Text style={styles.skipText}>{t('skip')}</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.content}>
            <View style={styles.logoContainer}>
              <Text style={styles.logoText}>PillLogic</Text>
            </View>

            <Text style={styles.title}>{t('welcomeToDocAid')}</Text>
            <Text style={styles.subtitle}>{t('loginToAccessAllFeatures')}</Text>

            {/* Email/Password Form - RESTORED */}
            <View style={styles.formContainer}>
              <View style={styles.inputContainer}>
                <Ionicons name="mail-outline" size={20} color={Colors.textSecondary} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Email"
                  value={email}
                  onChangeText={(text) => {
                    setEmail(text);
                    if (emailError) validateEmail(text);
                  }}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoComplete="email"
                />
              </View>
              {emailError ? <Text style={styles.errorText}>{emailError}</Text> : null}

              <View style={styles.inputContainer}>
                <Ionicons name="lock-closed-outline" size={20} color={Colors.textSecondary} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Password"
                  value={password}
                  onChangeText={(text) => {
                    setPassword(text);
                    if (passwordError) validatePassword(text);
                  }}
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                />
                <TouchableOpacity
                  onPress={() => setShowPassword(!showPassword)}
                  style={styles.passwordToggle}
                >
                  <Ionicons
                    name={showPassword ? "eye-off-outline" : "eye-outline"}
                    size={20}
                    color={Colors.textSecondary}
                  />
                </TouchableOpacity>
              </View>
              {passwordError ? <Text style={styles.errorText}>{passwordError}</Text> : null}

              <View style={styles.rememberForgotRow}>
                <View style={styles.rememberMeContainer}>
                  <Switch
                    value={rememberMe}
                    onValueChange={setRememberMe}
                    trackColor={{ false: Colors.border, true: Colors.docPurple.DEFAULT }}
                    thumbColor={Colors.white}
                  />
                  <Text style={styles.rememberMeText}>Remember me</Text>
                </View>
                <TouchableOpacity onPress={handleForgotPassword}>
                  <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
                </TouchableOpacity>
              </View>

              {/* Password Reset Warning */}
              <View style={styles.warningContainer}>
                <Ionicons name="warning-outline" size={16} color={Colors.warning} style={styles.warningIcon} />
                <Text style={styles.warningText}>
                  {t('passwordResetInDevelopment')}
                </Text>
              </View>

              <TouchableOpacity
                style={styles.signInButton}
                onPress={handleEmailSignIn}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator color={Colors.white} />
                ) : (
                  <Text style={styles.signInButtonText}>Sign In</Text>
                )}
              </TouchableOpacity>

              <View style={styles.dividerContainer}>
                <View style={styles.divider} />
                <Text style={styles.dividerText}>OR</Text>
                <View style={styles.divider} />
              </View>
            </View>

            {/* Social Login Buttons */}
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={styles.googleButton}
                onPress={handleGoogleSignIn}
                disabled={loading}
              >
                {googleSignInLoading ? (
                  <>
                    <ActivityIndicator size="small" color="#4285F4" style={styles.googleIcon} />
                    <Text style={styles.googleButtonText}>{t('loading')}</Text>
                  </>
                ) : (
                  <>
                    <Ionicons name="logo-google" size={24} color="#4285F4" style={styles.googleIcon} />
                    <Text style={styles.googleButtonText}>{t('continueWithGoogle')}</Text>
                  </>
                )}
              </TouchableOpacity>

              {/* Apple Sign-In Button - iOS only */}
              {Platform.OS === 'ios' && (
                <AppleAuthentication.AppleAuthenticationButton
                  buttonType={AppleAuthentication.AppleAuthenticationButtonType.SIGN_IN}
                  buttonStyle={AppleAuthentication.AppleAuthenticationButtonStyle.BLACK}
                  cornerRadius={BorderRadius.md}
                  style={styles.appleButton}
                  onPress={handleAppleSignIn}
                />
              )}

              {/* Recommended login note */}
              <Text style={styles.loginNote}>
                {t('recommendGoogleForSecurity')}
              </Text>
            </View>

            {/* Sign Up Link - RESTORED */}
            <View style={styles.signUpContainer}>
              <Text style={styles.signUpText}>Don't have an account? </Text>
              <TouchableOpacity onPress={handleSignUp}>
                <Text style={styles.signUpLink}>Sign Up</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.footer}>
            <Text style={styles.footerText}>{t('byLoggingInYouAgree')}</Text>
            <View style={styles.termsRow}>
              <TouchableOpacity>
                <Text style={styles.termsLink}>{t('termsOfService')}</Text>
              </TouchableOpacity>
              <Text style={styles.footerText}> {t('and')} </Text>
              <TouchableOpacity>
                <Text style={styles.termsLink}>{t('privacyPolicy')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* CAPTCHA Modal */}
      <CaptchaModal
        visible={isCaptchaModalVisible}
        onVerify={handleCaptchaVerify}
        onClose={closeCaptchaModal}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: 16,
    color: Colors.textPrimary,
    fontWeight: '500',
  },
  loadingSubText: {
    marginTop: Spacing.sm,
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: 'center',
    paddingHorizontal: Spacing.xl,
    fontStyle: 'italic',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: Spacing.md,
  },
  skipButton: {
    padding: Spacing.sm,
  },
  skipText: {
    color: Colors.textSecondary,
    fontSize: 16,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
    paddingBottom: Spacing.xl,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.docPurple.DEFAULT,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  logoText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.white,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginBottom: Spacing.lg,
    textAlign: 'center',
  },
  formContainer: {
    width: '100%',
    marginBottom: Spacing.md,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.sm,
    backgroundColor: Colors.white,
    height: 50,
  },
  inputIcon: {
    marginRight: Spacing.sm,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 16,
    color: Colors.textPrimary,
  },
  passwordToggle: {
    padding: Spacing.sm,
  },
  errorText: {
    color: Colors.error,
    fontSize: 12,
    marginBottom: Spacing.sm,
    marginLeft: Spacing.sm,
  },
  rememberForgotRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  rememberMeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rememberMeText: {
    marginLeft: Spacing.xs,
    fontSize: 14,
    color: Colors.textSecondary,
  },
  forgotPasswordText: {
    fontSize: 14,
    color: Colors.docPurple.DEFAULT,
  },
  signInButton: {
    backgroundColor: Colors.docPurple.DEFAULT,
    borderRadius: BorderRadius.md,
    paddingVertical: Spacing.md,
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  signInButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: Colors.border,
  },
  dividerText: {
    marginHorizontal: Spacing.md,
    color: Colors.textSecondary,
    fontSize: 14,
  },
  buttonContainer: {
    width: '100%',
  },
  googleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.md,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    marginBottom: Spacing.md,
  },
  googleIcon: {
    marginRight: Spacing.md,
  },
  googleButtonText: {
    fontSize: 16,
    color: Colors.textPrimary,
    fontWeight: '500',
  },
  appleButton: {
    width: '100%',
    height: 50,
    marginBottom: Spacing.md,
  },
  loginNote: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginTop: Spacing.sm,
    fontStyle: 'italic',
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3CD',
    borderColor: '#FFEAA7',
    borderWidth: 1,
    borderRadius: BorderRadius.sm,
    padding: Spacing.sm,
    marginBottom: Spacing.md,
  },
  warningIcon: {
    marginRight: Spacing.xs,
  },
  warningText: {
    flex: 1,
    fontSize: 12,
    color: '#856404',
    lineHeight: 16,
  },
  signUpContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: Spacing.md,
  },
  signUpText: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  signUpLink: {
    fontSize: 14,
    color: Colors.docPurple.DEFAULT,
    fontWeight: '600',
  },
  footer: {
    padding: Spacing.lg,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  termsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
    marginTop: Spacing.xs,
  },
  termsLink: {
    fontSize: 12,
    color: Colors.docPurple.DEFAULT,
  },
});

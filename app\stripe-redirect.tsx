import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { Text, Container, Button } from '../components/ui';
import { Colors } from '../constants/DesignSystem';
import { useLanguage } from '../contexts/LanguageContext';
import { useAuth } from '../contexts/AuthContext';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { supabase } from '../lib/supabase';

export default function StripeRedirectScreen() {
  const { t } = useLanguage();
  const { user, refreshUser } = useAuth();
  const router = useRouter();
  const params = useLocalSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');
  const [subscriptionTier, setSubscriptionTier] = useState<string | null>(null);

  useEffect(() => {
    const checkSubscriptionStatus = async () => {
      if (!user) {
        setStatus('error');
        setMessage(t('notLoggedIn'));
        return;
      }

      try {
        // Get the session ID from the URL if available
        const sessionId = params.session_id as string;

        console.log('Checking subscription status after redirect');
        console.log('User ID:', user.id);
        console.log('Session ID:', sessionId || 'Not available');

        // Call our fallback function to check subscription status
        const response = await fetch(
          'https://lckiptkbxurjxddeubew.functions.supabase.co/check-subscription-status',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
            },
            body: JSON.stringify({
              userId: user.id,
              sessionId: sessionId || undefined,
            }),
          }
        );

        if (!response.ok) {
          throw new Error(`Error checking subscription status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Subscription check response:', data);

        if (data.subscription_tier && data.subscription_tier !== 'free') {
          // User has an active subscription
          setStatus('success');
          setSubscriptionTier(data.subscription_tier);
          setMessage(t('subscriptionSuccess', { tier: data.subscription_tier }));

          // Refresh user data to get the updated subscription status
          console.log('Before refreshUser, current user tier:', user?.subscription_tier);
          await refreshUser();
          console.log('After refreshUser, updated user tier:', user?.subscription_tier);
        } else if (data.error) {
          // Error checking subscription
          setStatus('error');
          setMessage(t('subscriptionCheckError'));
          console.error('Error checking subscription:', data.error);
        } else {
          // No active subscription found
          setStatus('error');
          setMessage(t('subscriptionNotFound'));
        }
      } catch (error) {
        console.error('Error checking subscription status:', error);
        setStatus('error');
        setMessage(t('subscriptionCheckError'));
      }
    };

    checkSubscriptionStatus();
  }, [user, params]);

  const goToSubscription = () => {
    router.replace('/subscription');
  };

  const goToHome = () => {
    router.replace('/');
  };

  return (
    <Container>
      <View style={styles.container}>
        {status === 'loading' && (
          <>
            <ActivityIndicator size="large" color={Colors.primary} />
            <Text style={styles.message}>{t('checkingSubscription')}</Text>
          </>
        )}

        {status === 'success' && (
          <>
            <View style={styles.successIcon}>
              <Text style={styles.emoji}>✅</Text>
            </View>
            <Text style={styles.title}>{t('thankYou')}</Text>
            <Text style={styles.message}>{message}</Text>
            <Button onPress={goToHome} style={styles.button}>
              {t('goToHome')}
            </Button>
          </>
        )}

        {status === 'error' && (
          <>
            <View style={styles.errorIcon}>
              <Text style={styles.emoji}>❌</Text>
            </View>
            <Text style={styles.title}>{t('somethingWentWrong')}</Text>
            <Text style={styles.message}>{message}</Text>
            <Button onPress={goToSubscription} style={styles.button}>
              {t('tryAgain')}
            </Button>
            <Button onPress={goToHome} style={[styles.button, styles.secondaryButton]}>
              {t('goToHome')}
            </Button>
          </>
        )}
      </View>
    </Container>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  button: {
    marginTop: 20,
    minWidth: 200,
  },
  secondaryButton: {
    backgroundColor: Colors.secondary,
  },
  successIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.success,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  errorIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.error,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  emoji: {
    fontSize: 40,
  },
});

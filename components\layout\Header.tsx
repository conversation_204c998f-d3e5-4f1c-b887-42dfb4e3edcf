import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Colors, Spacing, BorderRadius } from '../../constants/DocAidDesign';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';
import { Language } from '../../services/languageTypes';

const Header: React.FC = () => {
  const router = useRouter();
  const { language, setLanguage, t } = useLanguage();
  const { user } = useAuth();
  const [showLanguageModal, setShowLanguageModal] = useState(false);

  const languages = [
    { code: Language.ENGLISH, name: 'English', flag: '🇺🇸' },
    { code: Language.VIETNAMESE, name: 'Tiếng Việt', flag: '🇻🇳' },
    { code: Language.HINDI, name: 'हिन्दी', flag: '🇮🇳' },
    { code: Language.CHINESE, name: '中文', flag: '🇨🇳' },
  ];

  const currentLanguage = languages.find(lang => lang.code === language);

  return (
    <>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.logoContainer}
          onPress={() => router.push('/')}
        >
          <Text style={styles.logoText}>PillLogic</Text>
        </TouchableOpacity>

        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.profileButton}
            onPress={() => router.push('/profile')}
          >
            {user ? (
              <View style={styles.userAvatar}>
                <Text style={styles.userInitial}>{user.email.charAt(0).toUpperCase()}</Text>
              </View>
            ) : (
              <Ionicons name="person-circle-outline" size={28} color={Colors.docPurple.DEFAULT} />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.languageButton}
            onPress={() => setShowLanguageModal(true)}
            hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
            activeOpacity={0.7}
          >
            <Text style={styles.languageFlag}>{currentLanguage?.flag}</Text>
            <Text style={styles.languageCode}>{currentLanguage?.code.toUpperCase()}</Text>
          </TouchableOpacity>
        </View>
      </View>

      <Modal
        visible={showLanguageModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowLanguageModal(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowLanguageModal(false)}
        >
          <View style={styles.languageModal}>
            <Text style={styles.modalTitle}>{t('selectLanguage')}</Text>

            {languages.map((lang) => (
              <TouchableOpacity
                key={lang.code}
                style={[
                  styles.languageOption,
                  language === lang.code && styles.selectedLanguage
                ]}
                onPress={() => {
                  setLanguage(lang.code);
                  setShowLanguageModal(false);
                }}
              >
                <Text style={styles.languageFlag}>{lang.flag}</Text>
                <Text style={styles.languageName}>{lang.name}</Text>
                {language === lang.code && (
                  <Ionicons name="checkmark" size={20} color={Colors.docPurple.DEFAULT} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  header: {
    position: 'absolute',
    top: 40, // Move header much lower to make it more accessible
    left: 0,
    right: 0,
    height: 70, // Reasonable height
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg, // Increased horizontal padding
    paddingBottom: 10, // Padding at the bottom
    paddingTop: 10, // Padding at the top
    zIndex: 10,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.docPurple.DEFAULT,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileButton: {
    marginRight: Spacing.md,
    padding: Spacing.xs,
  },
  userAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.docPurple.DEFAULT,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userInitial: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  languageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    minWidth: 80,
    minHeight: 40,
    justifyContent: 'center',
    marginBottom: 0,
    marginRight: 10, // Add right margin to prevent cutting off
    // Make the touch target larger without visible styling
    hitSlop: { top: 15, bottom: 15, left: 15, right: 15 },
  },
  languageFlag: {
    fontSize: 24, // Even larger flag
    marginRight: 8,
  },
  languageCode: {
    fontSize: 16, // Larger text
    fontWeight: '700',
    color: Colors.docPurple.DEFAULT,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  languageModal: {
    width: '80%',
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  languageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.lg, // More vertical padding for easier tapping
    paddingHorizontal: Spacing.md, // Added horizontal padding
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    minHeight: 60, // Minimum height for better touch target
  },
  selectedLanguage: {
    backgroundColor: Colors.docPurple.light + '40', // More visible selection
    borderRadius: BorderRadius.sm,
  },
  languageName: {
    flex: 1,
    fontSize: 18, // Larger text
    fontWeight: '500',
    color: Colors.textPrimary,
    marginLeft: Spacing.md, // More spacing
  },
});

export default Header;

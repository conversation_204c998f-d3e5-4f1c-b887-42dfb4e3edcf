// Environment variables for API keys and other sensitive information
import Constants from 'expo-constants';

// Roboflow API configuration
export const ROBOFLOW_API_KEY = Constants.expoConfig?.extra?.roboflowApiKey ||
                               process.env.EXPO_PUBLIC_ROBOFLOW_API_KEY ||
                               '';
export const ROBOFLOW_PROJECT_ID = Constants.expoConfig?.extra?.roboflowProjectId ||
                                  process.env.EXPO_PUBLIC_ROBOFLOW_PROJECT_ID ||
                                  'pills-detection-gyfka-tnhdg';
export const ROBOFLOW_VERSION = Constants.expoConfig?.extra?.roboflowVersion ||
                               process.env.EXPO_PUBLIC_ROBOFLOW_VERSION ||
                               '1';
export const ROBOFLOW_MODEL_URL = `https://serverless.roboflow.com/${ROBOFLOW_PROJECT_ID}/${ROBOFLOW_VERSION}`;

// Old Roboflow model information (kept as reference)
// export const OLD_ROBOFLOW_PROJECT_ID = 'chinese-cv-2-trgoh';
// export const OLD_ROBOFLOW_VERSION = '1';
// export const OLD_ROBOFLOW_MODEL_URL = `https://detect.roboflow.com/${OLD_ROBOFLOW_PROJECT_ID}/${OLD_ROBOFLOW_VERSION}`;

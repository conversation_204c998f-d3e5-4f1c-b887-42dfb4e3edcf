# Supabase
EXPO_PUBLIC_SUPABASE_URL=https://lckiptkbxurjxddeubew.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imxja2lwdGtieHVyanhkZGV1YmV3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU3NjQ4NDAsImV4cCI6MjA2MTM0MDg0MH0.DnBjnVKcIgE-eGrfXP6ttbEpsJqzD5EENwySo9eecow
SUPABASE_ACCESS_TOKEN=********************************************

# Resend API Key
EXPO_PUBLIC_RESEND_API_KEY=re_hUY21vWT_C6AKxToaSavFfvave8uYHt55

# hCAPTCHA
EXPO_PUBLIC_HCAPTCHA_SITE_KEY=f3a0d298-4616-4274-a5fc-45ae6796f7da


# API URL (keeping for other functions)
EXPO_PUBLIC_API_URL=https://lckiptkbxurjxddeubew.functions.supabase.co

# OpenAI API Key
EXPO_PUBLIC_OPENAI_API_KEY=********************************************************************************************************************************************************************

# Roboflow API Key
EXPO_PUBLIC_ROBOFLOW_API_KEY=RY8gN6VbwSYSDQWZA01b
EXPO_PUBLIC_ROBOFLOW_PROJECT_ID=pills-detection-gyfka-tnhdg
EXPO_PUBLIC_ROBOFLOW_VERSION=1

# DeepSeek API Key
EXPO_PUBLIC_DEEPSEEK_API_KEY=***********************************
EXPO_PUBLIC_DEEPSEEK_BASE_URL=https://api.deepseek.com

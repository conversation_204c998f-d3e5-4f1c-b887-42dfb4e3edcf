// Import our custom Resend client that works in React Native
import { resend } from './resendClient';
import Constants from 'expo-constants';

// Default sender email - using verified domain
export const DEFAULT_FROM_EMAIL = '<EMAIL>';

// Support email address
export const SUPPORT_EMAIL = '<EMAIL>';

// Email templates
export const sendWelcomeEmail = async (to: string, name: string = 'there') => {
  try {
    const { data, error } = await resend.emails.send({
      from: DEFAULT_FROM_EMAIL,
      to,
      subject: 'Welcome to PillLogic!',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #6941C6;">Welcome to PillLogic!</h1>
          <p>Hi ${name},</p>
          <p>Thank you for signing up for PillLogic. We're excited to have you on board!</p>
          <p>If you have any questions, feel free to reply to this email.</p>
          <p>Best regards,<br>The PillLogic Team</p>
        </div>
      `,
    });

    if (error) {
      console.error('Error sending welcome email:', error);
      return { success: false, error };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Exception sending welcome email:', error);
    return { success: false, error };
  }
};

// Password reset email
export const sendPasswordResetEmail = async (to: string, resetLink: string) => {
  try {
    // Ensure the reset link is properly encoded for email
    // This helps prevent issues with special characters in the URL
    // We need to be careful with the & character in HTML attributes
    const safeResetLink = resetLink.replace(/&/g, '&amp;');

    console.log('Sending password reset email with link:', resetLink);
    console.log('HTML-safe link for email:', safeResetLink);

    const { data, error } = await resend.emails.send({
      from: DEFAULT_FROM_EMAIL,
      to,
      subject: 'Reset Your PillLogic Password',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
          <h1 style="color: #6941C6; text-align: center; margin-bottom: 20px;">Reset Your Password</h1>

          <p>Hello,</p>

          <p>You recently requested to reset your password for your PillLogic account. Click the button below to reset it. <strong>This link is only valid for 1 hour.</strong></p>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${safeResetLink}" style="background-color: #6941C6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; display: inline-block;">Reset Password</a>
          </div>

          <p>If you did not request a password reset, please ignore this email or contact support if you have concerns.</p>

          <p><strong>IMPORTANT:</strong> This link must be opened on your mobile device where the PillLogic app is installed. Opening this link on a laptop or desktop computer will not work.</p>

          <p>If you don't see the reset password page after clicking the button, please close the app completely and reopen it.</p>

          <div style="background-color: #f8f4ff; padding: 15px; border-radius: 4px; margin: 20px 0;">
            <p style="margin: 0; color: #6941C6;"><strong>Tip:</strong> If you can't find this email in your inbox, please check your spam or junk folder.</p>
          </div>

          <p>Best regards,<br>The PillLogic Team</p>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666; text-align: center;">
            <p>If you're having trouble with the button above, copy and paste the URL below into your web browser:</p>
            <p style="word-break: break-all;">${safeResetLink}</p>
            <p>&copy; ${new Date().getFullYear()} PillLogic. All rights reserved.</p>
          </div>
        </div>
      `,
    });

    if (error) {
      console.error('Error sending password reset email:', error);
      return { success: false, error };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Exception sending password reset email:', error);
    return { success: false, error };
  }
};

// Email verification
export const sendVerificationEmail = async (to: string, verificationLink: string) => {
  try {
    const { data, error } = await resend.emails.send({
      from: DEFAULT_FROM_EMAIL,
      to,
      subject: 'Verify Your PillLogic Email',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #6941C6;">Verify Your Email</h1>
          <p>Thank you for signing up for PillLogic. Please verify your email address to complete your registration.</p>
          <p>Click the button below to verify your email:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationLink}" style="background-color: #6941C6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Verify Email</a>
          </div>
          <p>If you didn't create an account, you can safely ignore this email.</p>
          <p>This link will expire in 24 hours.</p>
          <p>Best regards,<br>The PillLogic Team</p>
        </div>
      `,
    });

    if (error) {
      console.error('Error sending verification email:', error);
      return { success: false, error };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Exception sending verification email:', error);
    return { success: false, error };
  }
};

// Support email
export const sendSupportEmail = async (
  userEmail: string,
  subject: string,
  message: string,
  userName: string = 'User'
) => {
  try {
    // Rate limiting check could be added here

    const { data, error } = await resend.emails.send({
      from: DEFAULT_FROM_EMAIL,
      to: SUPPORT_EMAIL,
      reply_to: userEmail,
      subject: `PillLogic Support: ${subject}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #6941C6;">PillLogic Support Request</h1>
          <p><strong>From:</strong> ${userName} (${userEmail})</p>
          <p><strong>Subject:</strong> ${subject}</p>
          <div style="margin: 20px 0; padding: 15px; background-color: #f5f5f5; border-left: 4px solid #6941C6;">
            <p><strong>Message:</strong></p>
            <p>${message.replace(/\\n/g, '<br>')}</p>
          </div>
          <p style="color: #666; font-size: 12px;">This message was sent from the PillLogic app's support form.</p>
        </div>
      `,
    });

    if (error) {
      console.error('Error sending support email:', error);
      return { success: false, error };
    }

    // Send confirmation to user
    const confirmationResult = await resend.emails.send({
      from: DEFAULT_FROM_EMAIL,
      to: userEmail,
      subject: 'Your PillLogic Support Request',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #6941C6;">Support Request Received</h1>
          <p>Hi ${userName},</p>
          <p>We've received your support request with the subject: <strong>${subject}</strong></p>
          <p>Our team will review your message and get back to you as soon as possible.</p>
          <div style="margin: 20px 0; padding: 15px; background-color: #f5f5f5; border-left: 4px solid #6941C6;">
            <p><strong>Your message:</strong></p>
            <p>${message.replace(/\\n/g, '<br>')}</p>
          </div>
          <p>Best regards,<br>The PillLogic Support Team</p>
        </div>
      `,
    });

    if (confirmationResult.error) {
      console.warn('Error sending confirmation email:', confirmationResult.error);
      // We still return success if the main support email was sent
    }

    return { success: true, data };
  } catch (error) {
    console.error('Exception sending support email:', error);
    return { success: false, error };
  }
};

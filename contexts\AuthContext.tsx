import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';
import { supabase, UserData, getCurrentUser } from '../lib/supabase';
import { Session } from '@supabase/supabase-js';
import { Alert, Linking, Platform, AppState, AppStateStatus } from 'react-native';
import * as WebBrowser from 'expo-web-browser';
import * as AuthSession from 'expo-auth-session';
import * as AppleAuthentication from 'expo-apple-authentication';
import * as Crypto from 'expo-crypto';
import Constants from 'expo-constants';
import { router } from 'expo-router';
import { sendVerificationEmail, sendPasswordResetEmail } from '../lib/resend';

// Detect environment
const isExpoGo = Constants.appOwnership === 'expo';
const isIOS = Platform.OS === 'ios';
const isDev = __DEV__;

// Define the shape of the auth context
interface AuthContextType {
  session: Session | null;
  user: UserData | null;
  loading: boolean;
  isAnonymous: boolean;
  signInWithGoogle: () => Promise<{ success: boolean; message: string }>;
  signInWithApple: () => Promise<{ success: boolean; message: string }>;
  signInWithEmail: (email: string, password: string, rememberMe?: boolean, captchaToken?: string) => Promise<{ success: boolean; message: string }>;
  signUpWithEmail: (email: string, password: string, captchaToken?: string) => Promise<{ success: boolean; message: string }>;
  signInAnonymously: (captchaToken?: string) => Promise<{ success: boolean; message: string }>;
  resetPassword: (email: string, captchaToken?: string) => Promise<{ success: boolean; message: string }>;
  updatePassword: (password: string) => Promise<{ success: boolean; message: string }>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

// Create the auth context with default values
const AuthContext = createContext<AuthContextType>({
  session: null,
  user: null,
  loading: true,
  isAnonymous: false,
  signInWithGoogle: async () => { return { success: false, message: 'Not implemented' }; },
  signInWithApple: async () => { return { success: false, message: 'Not implemented' }; },
  signInWithEmail: async () => { return { success: false, message: 'Not implemented' }; },
  signUpWithEmail: async () => { return { success: false, message: 'Not implemented' }; },
  signInAnonymously: async () => { return { success: false, message: 'Not implemented' }; },
  resetPassword: async () => { return { success: false, message: 'Not implemented' }; },
  updatePassword: async () => { return { success: false, message: 'Not implemented' }; },
  signOut: async () => {},
  refreshUser: async () => {},
});

// Auth provider props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth provider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAnonymous, setIsAnonymous] = useState(false);

  // Initialize auth state
  useEffect(() => {
    console.log('AuthProvider initialized');

    // Get the current session
    supabase.auth.getSession().then(({ data: { session }, error }) => {
      console.log('Initial session check:', session ? 'Session found' : 'No session');
      if (error) {
        console.error('Error getting initial session:', error);
      }

      // If we have a session, validate it
      if (session) {
        console.log('Session found, validating...');

        // Check if the session is expired
        const now = Math.floor(Date.now() / 1000);
        const expiresAt = session.expires_at;

        if (expiresAt && now >= expiresAt) {
          console.log('Session is expired, attempting to refresh...');

          // Try to refresh the session
          supabase.auth.refreshSession().then(({ data, error: refreshError }) => {
            if (refreshError) {
              console.error('Error refreshing session:', refreshError);
              setSession(null);
              setLoading(false);
              return;
            }

            if (data.session) {
              console.log('Session refreshed successfully');
              setSession(data.session);
              loadUserData();
            } else {
              console.log('No session after refresh, user needs to log in again');
              setSession(null);
              setLoading(false);
            }
          });
        } else {
          // Session is valid
          console.log('Session is valid, loading user data');
          setSession(session);
          loadUserData();
        }
      } else {
        console.log('No initial session, setting loading to false');
        setLoading(false);
      }
    });

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log(`Supabase auth event: ${event}`, session ? 'with session' : 'without session');

        if (event === 'TOKEN_REFRESHED') {
          console.log('Token was refreshed automatically by Supabase');
        }

        setSession(session);

        if (session) {
          console.log('Auth state changed with session, loading user data');
          await loadUserData();

          // Log session expiry time for debugging
          if (session.expires_at) {
            const expiresAt = new Date(session.expires_at * 1000).toLocaleString();
            const now = new Date().toLocaleString();
            console.log(`Session expires at: ${expiresAt} (current time: ${now})`);

            // Calculate time until expiry
            const secondsUntilExpiry = session.expires_at - Math.floor(Date.now() / 1000);
            const minutesUntilExpiry = Math.floor(secondsUntilExpiry / 60);
            console.log(`Session expires in: ${minutesUntilExpiry} minutes (${secondsUntilExpiry} seconds)`);
          }
        } else {
          console.log('Auth state changed without session, clearing user');
          setUser(null);
          setLoading(false);
        }
      }
    );

    // Set up a session refresh mechanism for long app sessions
    const refreshTimer = setInterval(() => {
      if (session) {
        // Check if we need to refresh (e.g., if less than 30 minutes remaining)
        if (session.expires_at) {
          const now = Math.floor(Date.now() / 1000);
          const secondsUntilExpiry = session.expires_at - now;
          const minutesUntilExpiry = Math.floor(secondsUntilExpiry / 60);

          // If less than 30 minutes remaining, refresh the token
          if (minutesUntilExpiry < 30) {
            console.log(`Session expires in ${minutesUntilExpiry} minutes, refreshing...`);
            supabase.auth.refreshSession().then(({ data, error }) => {
              if (error) {
                console.error('Error refreshing session:', error);
              } else if (data.session) {
                console.log('Session refreshed successfully');
                setSession(data.session);
              }
            });
          }
        }
      }
    }, 5 * 60 * 1000); // Check every 5 minutes

    // Clean up the timer and subscription when the component unmounts
    return () => {
      subscription.unsubscribe();
      clearInterval(refreshTimer);
    };
  }, []);

  // Load user data from Supabase with retry logic
  const loadUserData = async (retryCount = 0) => {
    setLoading(true);
    try {
      console.log(`Loading user data (attempt ${retryCount + 1})...`);

      // Verify we have a valid session before loading user data
      const { data: { session: currentSession } } = await supabase.auth.getSession();

      if (!currentSession) {
        console.log('No valid session found when trying to load user data');

        // If this is not the last retry attempt, wait and try again
        if (retryCount < 2) {
          console.log(`Retrying session check in 1 second (attempt ${retryCount + 1})`);
          await new Promise(resolve => setTimeout(resolve, 1000));
          return loadUserData(retryCount + 1);
        }

        setUser(null);
        setIsAnonymous(false);
        setLoading(false);
        return;
      }

      // Check if this is an anonymous session
      const isAnonymousSession = currentSession.user?.app_metadata?.provider === 'anonymous';
      setIsAnonymous(isAnonymousSession);

      if (isAnonymousSession) {
        console.log('Anonymous user detected');
        // For anonymous users, we create a minimal user object
        setUser({
          id: currentSession.user.id,
          email: '<EMAIL>',
          created_at: currentSession.user.created_at || new Date().toISOString(),
          subscription_tier: 'free'
        });
        setLoading(false);
        return;
      }

      // Check if the session is about to expire
      if (currentSession.expires_at) {
        const now = Math.floor(Date.now() / 1000);
        const secondsUntilExpiry = currentSession.expires_at - now;

        // If less than 10 minutes remaining, refresh the token before loading user data
        if (secondsUntilExpiry < 10 * 60) {
          console.log(`Session expires in ${Math.floor(secondsUntilExpiry / 60)} minutes, refreshing before loading user data...`);

          const { data, error } = await supabase.auth.refreshSession();

          if (error) {
            console.error('Error refreshing session before loading user data:', error);
            // Continue anyway with the current session
          } else if (data.session) {
            console.log('Session refreshed successfully before loading user data');
            setSession(data.session);
          }
        }
      }

      // Load user data - force a fresh fetch from the database
      const userData = await getCurrentUser(true);

      // Verify the user data matches the session user
      if (userData && currentSession.user.id === userData.id) {
        console.log('User data loaded successfully and verified');
        console.log('User subscription tier:', userData.subscription_tier);
        setUser(userData);
      } else {
        console.error('User data mismatch or missing - session user ID:',
                     currentSession.user.id, 'loaded user ID:', userData?.id);

        // If this is not the last retry attempt, wait and try again
        if (retryCount < 2 && currentSession) {
          console.log(`User data mismatch, retrying in 1 second (attempt ${retryCount + 1})`);
          await new Promise(resolve => setTimeout(resolve, 1000));
          return loadUserData(retryCount + 1);
        }

        // Handle the mismatch - could be a security issue
        if (!userData) {
          console.log('No user data found, clearing session');
          await supabase.auth.signOut();
          setSession(null);
          setUser(null);
        }
      }
    } catch (error) {
      console.error('Error loading user data:', error);

      // If this is not the last retry attempt, wait and try again
      if (retryCount < 2) {
        console.log(`Error loading user data, retrying in 1 second (attempt ${retryCount + 1})`);
        await new Promise(resolve => setTimeout(resolve, 1000));
        return loadUserData(retryCount + 1);
      }

      // On error, we should clear the user but keep the session
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  // Refresh user data
  const refreshUser = async () => {
    console.log('refreshUser called, loading fresh user data...');
    await loadUserData();
    console.log('User data refreshed, new subscription tier:', user?.subscription_tier);
  };

  // Monitor app state changes to verify session when app comes to foreground
  useEffect(() => {
    console.log('Setting up app state change listener for session monitoring');

    // Function to handle app state changes
    const handleAppStateChange = async (nextAppState: AppStateStatus) => {
      console.log('App state changed to:', nextAppState);

      // When app comes to foreground (active)
      if (nextAppState === 'active') {
        console.log('App came to foreground, verifying session...');

        // Only proceed if we think we have a session
        if (session) {
          try {
            // Verify the session is still valid
            const { data: { session: currentSession }, error } = await supabase.auth.getSession();

            if (error) {
              console.error('Error checking session on app foreground:', error);
            } else if (!currentSession) {
              console.log('Session is no longer valid on app foreground');
              setSession(null);
              setUser(null);

              // Optionally alert the user
              Alert.alert(
                'Session Expired',
                'Your session has expired. Please sign in again.',
                [{ text: 'OK', onPress: () => router.replace('/auth/login') }]
              );
            } else {
              console.log('Session is still valid on app foreground');

              // Check if session is about to expire and refresh if needed
              if (currentSession.expires_at) {
                const now = Math.floor(Date.now() / 1000);
                const secondsUntilExpiry = currentSession.expires_at - now;
                const minutesUntilExpiry = Math.floor(secondsUntilExpiry / 60);

                console.log(`Session expires in ${minutesUntilExpiry} minutes`);

                // If less than 30 minutes remaining, refresh the token
                if (minutesUntilExpiry < 30) {
                  console.log('Session is about to expire, refreshing...');
                  const { data, error: refreshError } = await supabase.auth.refreshSession();

                  if (refreshError) {
                    console.error('Error refreshing session on app foreground:', refreshError);
                  } else if (data.session) {
                    console.log('Session refreshed successfully on app foreground');
                    setSession(data.session);
                  }
                }
              }

              // Refresh user data when app comes to foreground
              await loadUserData();
            }
          } catch (e) {
            console.error('Exception during session check on app foreground:', e);
          }
        }
      }
    };

    // Subscribe to app state changes
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Clean up the subscription when the component unmounts
    return () => {
      subscription.remove();
    };
  }, [session, router]);

  // Sign in with Google using Expo's AuthSession
  const signInWithGoogle = async (): Promise<{ success: boolean; message: string }> => {
    try {
      console.log('Starting Google sign in process with Expo AuthSession...');
      console.log('Environment:', { isExpoGo, isIOS, isDev, platform: Platform.OS });

      // Show loading indicator
      setLoading(true);

      // Track redirects for debugging
      const redirectHistory = [];

      // Force clear any existing session to prevent conflicts
      await supabase.auth.signOut();

      // Use the auth.expo.io proxy for development
      // Configure redirect URI based on environment
      let redirectUri;

      // In Expo Go, we MUST use the auth.expo.io proxy
      if (isExpoGo) {
        // For Expo Go, we need to use the auth.expo.io URL
        // This is critical - deep links to pilllogic:// won't work in Expo Go
        redirectUri = AuthSession.makeRedirectUri({
          preferLocalhost: false,
        });

        // Verify that we're using the auth.expo.io URL
        if (!redirectUri.includes('auth.expo.io')) {
          console.warn('Warning: Redirect URI does not contain auth.expo.io, which may cause issues in Expo Go');
          // Force the correct format if needed
          redirectUri = `https://auth.expo.io/@chaupham1092/pilllogic`;
        }

        console.log('Using Expo Go specific redirect URI:', redirectUri);
      } else {
        // For production builds, we can use the native URI
        redirectUri = AuthSession.makeRedirectUri({
          native: 'pilllogic://auth/callback',
        });
        console.log('Using production redirect URI:', redirectUri);
      }

      // Create the auth request URL - DO NOT add state parameter, let Supabase handle it
      const supabaseAuthUrl = 'https://lckiptkbxurjxddeubew.supabase.co/auth/v1/authorize';
      let authRequestUrl = `${supabaseAuthUrl}?provider=google&redirect_to=${encodeURIComponent(redirectUri)}`;

      // Add additional parameters for development/Expo Go
      if (isDev) {
        // Add a development mode flag but DO NOT add a state parameter
        // Supabase handles the state parameter internally
        authRequestUrl += `&dev_mode=true`;

        // Log that we're in development mode
        console.log('Added development mode flag to auth URL');
      }

      console.log('Auth request URL:', authRequestUrl);

      // Set up a listener for deep links before opening the browser
      let urlListener: any = null;
      let hasHandledRedirect = false;

      // Helper function to safely log URLs with tokens
      const safelyLogUrl = (url: string) => {
        if (!url) return 'No URL';

        // Check if URL has a fragment
        if (url.includes('#')) {
          const baseUrl = url.split('#')[0];
          const fragment = url.split('#')[1];

          // Check if fragment contains tokens
          if (fragment.includes('access_token=')) {
            // Log base URL and indicate tokens are present
            return `${baseUrl}#[access_token and other params present]`;
          }
        }

        return url;
      };

      // Create a promise that will resolve when we get a deep link
      const deepLinkPromise = new Promise<string | null>((resolve) => {
        // First check if we already have a deep link (might have been received before we set up the listener)
        Linking.getInitialURL().then(initialUrl => {
          if (initialUrl) {
            console.log('Initial deep link found:', safelyLogUrl(initialUrl));

            // Check if this URL contains tokens
            const hasAccessToken = initialUrl.includes('#') &&
                                  initialUrl.split('#')[1].includes('access_token=');
            const hasCode = initialUrl.includes('code=');

            if (hasAccessToken || hasCode) {
              console.log('Initial deep link contains authentication data');
              hasHandledRedirect = true;
              resolve(initialUrl);
              return;
            }
          }

          // If no initial deep link with tokens, set up the listener
          console.log('Setting up deep link listener');
          urlListener = Linking.addEventListener('url', (event) => {
            console.log('Deep link received:', safelyLogUrl(event.url));

            // Check if this URL contains tokens - be sure to check in the fragment part
            const hasAccessToken = event.url.includes('#') &&
                                  event.url.split('#')[1].includes('access_token=');
            const hasCode = event.url.includes('code=');

            if (hasAccessToken || hasCode) {
              console.log('Deep link contains authentication data:',
                          hasAccessToken ? 'access_token present' : 'code present');
              hasHandledRedirect = true;
              resolve(event.url);
            }
          });

          // Set a timeout to resolve with null if no deep link is received
          setTimeout(() => {
            if (!hasHandledRedirect) {
              console.log('Deep link timeout - no URL received');
              resolve(null);
            }
          }, 30000); // 30 second timeout (reduced from 60 seconds)
        }).catch(error => {
          console.error('Error getting initial URL:', error);

          // Set up the listener anyway as fallback
          urlListener = Linking.addEventListener('url', (event) => {
            console.log('Deep link received (after getInitialURL error):', safelyLogUrl(event.url));

            // Check if this URL contains tokens
            const hasAccessToken = event.url.includes('#') &&
                                  event.url.split('#')[1].includes('access_token=');
            const hasCode = event.url.includes('code=');

            if (hasAccessToken || hasCode) {
              console.log('Deep link contains authentication data');
              hasHandledRedirect = true;
              resolve(event.url);
            }
          });

          // Set a timeout to resolve with null if no deep link is received
          setTimeout(() => {
            if (!hasHandledRedirect) {
              console.log('Deep link timeout - no URL received');
              resolve(null);
            }
          }, 30000); // 30 second timeout
        });
      });

      // Ensure WebBrowser is ready to handle redirects
      WebBrowser.maybeCompleteAuthSession();

      // For Expo Go on iOS, check if we already have a session before starting the auth flow
      if (isExpoGo && isIOS) {
        try {
          console.log('Checking for existing session before starting auth flow...');
          const { data: { session: existingSession } } = await supabase.auth.getSession();

          if (existingSession) {
            console.log('Found existing session - no need to authenticate again');
            setSession(existingSession);
            await loadUserData();
            // No need for Alert dialog as the login screen will handle the redirect
            return { success: true, message: 'You are already signed in!' };
          }
        } catch (e) {
          console.error('Error checking for existing session:', e);
        }
      }

      console.log('Opening auth URL in browser...');

      // Make sure any previous auth sessions are completed
      WebBrowser.maybeCompleteAuthSession();

      // Open the auth request URL in a web browser
      const browserPromise = WebBrowser.openAuthSessionAsync(
        authRequestUrl,
        redirectUri, // Explicitly provide the redirect URI
        {
          showInRecents: true,
          createTask: true,
          preferEphemeralSession: false,
          windowFeatures: {
            timeout: 60000, // Increase timeout to 60 seconds to give more time for authentication
          }
        }
      ).catch(error => {
        console.error('Error opening auth session:', error);
        // Return a dummy result to prevent the Promise.all from failing
        return { type: 'error', url: null };
      });

      // Use Promise.allSettled instead of Promise.all to handle failures gracefully
      const results = await Promise.allSettled([
        browserPromise,
        deepLinkPromise
      ]);

      // Extract the results, handling potential failures
      const browserResult = results[0].status === 'fulfilled'
        ? results[0].value
        : { type: 'error', url: null };

      const deepLinkUrl = results[1].status === 'fulfilled'
        ? results[1].value
        : null;

      console.log('Browser promise result status:', results[0].status);
      console.log('Deep link promise result status:', results[1].status);

      // Clean up the URL listener
      if (urlListener) {
        urlListener.remove();
      }

      console.log('Auth session result type:', browserResult.type);
      console.log('Deep link captured:', hasHandledRedirect ? 'Yes' : 'No');

      // Special handling for Expo Go on iOS
      if (isExpoGo && isIOS) {
        console.log('Special handling for Expo Go on iOS');

        // In Expo Go on iOS, we need to check if the browser was cancelled
        if (browserResult.type === 'cancel') {
          console.log('Browser was cancelled - this is expected when user closes the auth tab');

          // CRITICAL: For Expo Go on iOS, we need to manually extract the token
          // This is because the deep link listener doesn't work properly in Expo Go
          console.log('Attempting manual token extraction for Expo Go on iOS...');

          try {
            // Try to get the URL directly from Linking
            console.log('Checking for any deep links that might have been missed...');
            const initialUrl = await Linking.getInitialURL();

            if (initialUrl && initialUrl.includes('#access_token=')) {
              console.log('Found access token in initialUrl - attempting direct extraction');

              // Extract tokens using regex
              const accessTokenMatch = initialUrl.match(/access_token=([^&]+)/);
              const refreshTokenMatch = initialUrl.match(/refresh_token=([^&]+)/);

              if (accessTokenMatch && refreshTokenMatch) {
                const extractedAccessToken = accessTokenMatch[1];
                const extractedRefreshToken = refreshTokenMatch[1];

                console.log('Successfully extracted tokens from initialUrl');

                // Try to set the session with these tokens
                const { data, error } = await supabase.auth.setSession({
                  access_token: extractedAccessToken,
                  refresh_token: extractedRefreshToken
                });

                if (error) {
                  console.error('Error setting session with extracted tokens:', error);
                } else if (data.session) {
                  console.log('Successfully set session with extracted tokens!');
                  setSession(data.session);
                  await loadUserData();
                  // No need for Alert dialog as the login screen will handle the redirect
                  return { success: true, message: 'You have been signed in successfully!' };
                }
              }
            }

            // Simplified approach - just navigate to the callback screen
            // This is more reliable than trying to do everything here
            console.log('Direct extraction failed - navigating to callback screen for recovery');
            router.push({
              pathname: '/auth/callback',
              params: {
                expo_go_ios: 'true',
                auth_time: new Date().getTime().toString(),
                manual_recovery: 'true'
              }
            });
            return { success: false, message: 'Authentication failed. Redirecting to callback screen.' };
          } catch (e) {
            console.error('Error during manual token extraction:', e);

            // Navigate to callback as a fallback
            console.log('Navigating to callback screen as fallback after error');
            router.push('/auth/callback');
            return { success: false, message: 'Authentication failed. Redirecting to callback screen.' };
          }
        }
      }

      // Special handling for the case where the user closed the browser but we already captured the URL
      if (browserResult.type === 'cancel' && hasHandledRedirect && deepLinkUrl) {
        console.log('Browser was cancelled but we already captured the authentication URL via deep linking');
        console.log('This is the expected behavior when user closes the browser after successful authentication');
      }

      // If we got a deep link with tokens, use that instead of the browser result
      const finalUrl = hasHandledRedirect && deepLinkUrl ?
        deepLinkUrl :
        (browserResult.type === 'success' ? browserResult.url : null);

      console.log('Final URL determined:', finalUrl ? 'URL captured' : 'No URL captured');

      if (finalUrl) {
        console.log('Auth success with URL:', safelyLogUrl(finalUrl));
        redirectHistory.push({ timestamp: new Date().toISOString(), url: finalUrl });

        // Log the full URL for debugging (safely)
        console.log('Full redirect URL:', safelyLogUrl(finalUrl));

        // Extract error information if present
        if (finalUrl.includes('error=')) {
          const errorParams = new URLSearchParams(finalUrl.split('?')[1]);
          const error = errorParams.get('error');
          const errorDescription = errorParams.get('error_description');
          const errorCode = errorParams.get('error_code');

          console.error('Auth error in redirect:', { error, errorCode, errorDescription });

          // Special handling for bad_oauth_state error
          if (errorCode === 'bad_oauth_state') {
            console.log('Detected bad_oauth_state error. This is likely due to state parameter mismatch.');
            console.log('Navigating to callback to attempt recovery...');

            // Navigate to callback screen which has special handling for this error
            router.push('/auth/callback');
            return { success: false, message: 'Authentication failed. Redirecting to callback screen.' };
          }

          // Even with an error, try to extract tokens (they might still be present)
          console.log('Attempting to extract tokens despite error...');
        }

        // Enhanced token extraction for multiple redirect scenarios
        let accessToken = null;
        let refreshToken = null;
        let authCode = null;

        // Extract from URL fragment (after #)
        if (finalUrl.includes('#')) {
          try {
            const fragmentParams = new URLSearchParams(finalUrl.split('#')[1]);
            accessToken = accessToken || fragmentParams.get('access_token');
            refreshToken = refreshToken || fragmentParams.get('refresh_token');
            console.log('Extracted tokens from URL fragment:', !!accessToken, !!refreshToken);
          } catch (e) {
            console.error('Error parsing URL fragment:', e);
          }
        }

        // Extract from URL query params - handle multiple query parameter sections
        if (finalUrl.includes('?')) {
          try {
            const queryParts = finalUrl.split('?').slice(1);
            for (const part of queryParts) {
              const cleanPart = part.split('#')[0];
              const queryParams = new URLSearchParams(cleanPart);

              // Check for tokens in this query part
              const partAccessToken = queryParams.get('access_token');
              const partRefreshToken = queryParams.get('refresh_token');
              const partCode = queryParams.get('code');

              // Use the first valid tokens/code we find
              accessToken = accessToken || partAccessToken;
              refreshToken = refreshToken || partRefreshToken;
              authCode = authCode || partCode;

              console.log(`Extracted from query part: token=${!!partAccessToken}, refresh=${!!partRefreshToken}, code=${!!partCode}`);
            }
          } catch (e) {
            console.error('Error parsing URL query parts:', e);
          }
        }

        // Special handling for iOS - try regex extraction as a fallback
        if (isIOS && (!accessToken || !refreshToken)) {
          try {
            const iosTokenMatch = finalUrl.match(/access_token=([^&]+)/);
            const iosRefreshMatch = finalUrl.match(/refresh_token=([^&]+)/);

            if (iosTokenMatch && iosRefreshMatch) {
              accessToken = accessToken || iosTokenMatch[1];
              refreshToken = refreshToken || iosRefreshMatch[1];
              console.log('Extracted tokens using iOS-specific regex method');
            }
          } catch (e) {
            console.error('Error in iOS-specific token extraction:', e);
          }
        }

        console.log('Final extraction results:', {
          hasAccessToken: !!accessToken,
          hasRefreshToken: !!refreshToken,
          hasAuthCode: !!authCode
        });

        // If we have tokens, try to set the session
        if (accessToken && refreshToken) {
          console.log('Attempting to set session with extracted tokens');

          try {
            const { data, error } = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken,
            });

            if (error) {
              console.error('Error setting session with tokens:', error);

              // Navigate to callback screen with tokens as params
              console.log('Navigating to callback with tokens...');
              router.push({
                pathname: '/auth/callback',
                params: { access_token: accessToken, refresh_token: refreshToken }
              });
              return { success: false, message: 'Authentication failed. Redirecting to callback screen with tokens.' };
            }

            // If we successfully set the session, load user data and navigate to home
            if (data.session) {
              console.log('Session set successfully with tokens from Google sign-in');
              setSession(data.session);

              // Load user data immediately
              await loadUserData();

              // Return success - the login screen will handle navigation
              setLoading(false);
              return { success: true, message: 'Successfully signed in with Google!' };
            } else {
              console.error('No session returned after setting tokens');
              return { success: false, message: 'Failed to establish session' };
            }
          } catch (sessionError) {
            console.error('Exception during session setting:', sessionError);
          }
        }

        // Try to use auth code if available
        if (authCode) {
          console.log('Attempting to exchange auth code for session:', authCode);

          try {
            const { data, error } = await supabase.auth.exchangeCodeForSession(authCode);

            if (error) {
              console.error('Error exchanging code for session:', error);
            } else if (data.session) {
              console.log('Session obtained from code exchange');
              setSession(data.session);
              await loadUserData();
              // No need for Alert dialog as the login screen will handle the redirect
              return { success: true, message: 'You have been signed in successfully!' };
            } else {
              console.log('No session from code exchange');
            }
          } catch (exchangeError) {
            console.error('Exception during code exchange:', exchangeError);
          }
        }

        // Check if we have a session after the redirect
        try {
          console.log('Checking for existing session');
          const { data: { session }, error: sessionError } = await supabase.auth.getSession();

          if (sessionError) {
            console.error('Error getting session after redirect:', sessionError);
          } else if (session) {
            console.log('Session found after redirect!');
            setSession(session);
            await loadUserData();
            // No need for Alert dialog as the login screen will handle the redirect
            return { success: true, message: 'You have been signed in successfully!' };
          } else {
            console.log('No session found after redirect');
          }
        } catch (sessionCheckError) {
          console.error('Exception during session check:', sessionCheckError);
        }

        // As a last resort, navigate to callback
        console.log('All direct authentication methods failed, navigating to callback...');

        // Pass any tokens or code we might have found
        const callbackParams: Record<string, string> = {};
        if (accessToken) callbackParams.access_token = accessToken;
        if (refreshToken) callbackParams.refresh_token = refreshToken;
        if (authCode) callbackParams.code = authCode;

        router.push({
          pathname: '/auth/callback',
          params: callbackParams
        });
      } else if (browserResult.type !== 'success' && !finalUrl) {
        // Only show the cancellation message if we didn't get a URL from either method
        console.log('Auth was dismissed or failed, and no deep link was captured');
        Alert.alert('Authentication Cancelled', 'The authentication process was cancelled or failed. Please try again.');
      }
    } catch (error) {
      console.error('Error signing in with Google:', error);
      Alert.alert('Error', 'Failed to sign in with Google. Please try again.');

      // Even on error, try navigating to callback as a last resort
      if (isDev) {
        console.log('Navigating to callback after error as last resort');
        router.push('/auth/callback');
      }
    } finally {
      // Make sure we're not stuck in loading state
      console.log('Ensuring loading state is reset');
      setLoading(false);
    }

    // If we reach here, authentication failed
    return { success: false, message: 'Authentication failed. Please try again.' };
  };

  // Sign in with Apple
  const signInWithApple = async (): Promise<{ success: boolean; message: string }> => {
    try {
      console.log('Starting Apple sign in process...');

      // Check if Apple Authentication is available (iOS only)
      if (!isIOS) {
        return { success: false, message: 'Apple Sign-In is only available on iOS devices.' };
      }

      const isAvailable = await AppleAuthentication.isAvailableAsync();
      if (!isAvailable) {
        return { success: false, message: 'Apple Sign-In is not available on this device.' };
      }

      setLoading(true);

      // Request Apple authentication
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      console.log('Apple credential received:', {
        user: credential.user,
        email: credential.email,
        fullName: credential.fullName,
        identityToken: !!credential.identityToken,
        authorizationCode: !!credential.authorizationCode,
      });

      // Sign in with Supabase using the Apple credential
      const { data, error } = await supabase.auth.signInWithIdToken({
        provider: 'apple',
        token: credential.identityToken!,
        nonce: credential.nonce,
      });

      if (error) {
        console.error('Error signing in with Apple:', error);
        return { success: false, message: error.message || 'Failed to sign in with Apple.' };
      }

      if (data.session) {
        console.log('Apple sign in successful');
        setSession(data.session);
        await loadUserData();
        return { success: true, message: 'Successfully signed in with Apple!' };
      } else {
        console.log('No session returned from Apple sign in');
        return { success: false, message: 'Failed to create session with Apple.' };
      }

    } catch (error: any) {
      console.error('Error signing in with Apple:', error);

      // Handle user cancellation
      if (error.code === 'ERR_CANCELED') {
        return { success: false, message: 'Apple Sign-In was cancelled.' };
      }

      return { success: false, message: 'Failed to sign in with Apple. Please try again.' };
    } finally {
      setLoading(false);
    }
  };

  // Sign in with email and password
  const signInWithEmail = async (
    email: string,
    password: string,
    rememberMe: boolean = true,
    captchaToken?: string
  ): Promise<{ success: boolean; message: string }> => {
    try {
      console.log('Signing in with email and password...');
      console.log('Environment:', { isExpoGo, isIOS, isDev, platform: Platform.OS });
      setLoading(true);

      // Validate email and password
      if (!email || !password) {
        return { success: false, message: 'Email and password are required' };
      }

      // Attempt to sign in with persistence option based on rememberMe
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
        options: {
          // Skip CAPTCHA verification for email sign-in unless a token is provided
          captchaToken: captchaToken || undefined
        }
      });

      // If rememberMe is false and sign-in was successful, we'll set a shorter session expiry
      // This is a workaround since Supabase doesn't directly support non-persistent sessions
      if (!rememberMe && data.session) {
        console.log('User chose not to be remembered - will use shorter session duration');
      }

      if (error) {
        console.error('Error signing in with email:', error);

        // Handle specific error cases
        if (error.message.includes('Invalid login credentials')) {
          return { success: false, message: 'Invalid email or password' };
        } else if (error.message.includes('Email not confirmed')) {
          return { success: false, message: 'Please verify your email before signing in' };
        } else if (error.message.includes('rate limit')) {
          return { success: false, message: 'Too many attempts. Please try again later' };
        }

        return { success: false, message: error.message };
      }

      if (!data.session) {
        console.error('No session returned after successful sign in');
        return { success: false, message: 'Authentication failed. Please try again' };
      }

      // Set session and load user data
      console.log('Successfully signed in with email');

      try {
        // Double-check the session is valid
        const { error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          console.error('Error validating session after sign in:', sessionError);

          // Try to set the session explicitly
          const { error: setSessionError } = await supabase.auth.setSession({
            access_token: data.session.access_token,
            refresh_token: data.session.refresh_token,
          });

          if (setSessionError) {
            console.error('Error setting session after sign in:', setSessionError);
            return { success: false, message: 'Authentication succeeded but session creation failed. Please try again' };
          }
        }

        // Explicitly set the session and load user data
        setSession(data.session);
        await loadUserData();

        // Force navigation to home screen
        setLoading(false);
        router.replace('/');
        return { success: true, message: 'Successfully signed in!' };
      } catch (sessionError) {
        console.error('Exception handling session after sign in:', sessionError);
        return { success: false, message: 'Authentication succeeded but session creation failed. Please try again' };
      }
    } catch (error) {
      console.error('Exception during email sign in:', error);
      return { success: false, message: 'An unexpected error occurred. Please try again' };
    } finally {
      setLoading(false);
    }
  };

  // Sign up with email and password
  const signUpWithEmail = async (email: string, password: string, captchaToken?: string): Promise<{ success: boolean; message: string }> => {
    try {
      console.log('Signing up with email and password...');
      console.log('Environment:', { isExpoGo, isIOS, isDev, platform: Platform.OS });
      setLoading(true);

      // Validate email and password
      if (!email || !password) {
        return { success: false, message: 'Email and password are required' };
      }

      // Validate password strength
      if (password.length < 8) {
        return { success: false, message: 'Password must be at least 8 characters long' };
      }

      // Determine redirect URL based on environment
      let redirectTo;
      if (isExpoGo) {
        // For Expo Go, we need to use the auth.expo.io proxy
        // But we'll redirect to our verify screen instead of the callback
        redirectTo = `https://auth.expo.io/@chaupham1092/pilllogic/--/auth/verify`;
        console.log('Using Expo Go redirect URL for email confirmation:', redirectTo);
      } else {
        // For standalone apps, we can use our custom URL scheme
        redirectTo = AuthSession.makeRedirectUri({
          native: 'pilllogic://auth/verify',
        });
        console.log('Using native redirect URL for email confirmation:', redirectTo);
      }

      // Attempt to sign up
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: redirectTo,
          // Use CAPTCHA token if provided
          captchaToken: captchaToken,
          data: {
            // Additional user metadata can be added here
            signup_date: new Date().toISOString(),
          }
        }
      });

      if (error) {
        console.error('Error signing up with email:', error);

        // Handle specific error cases
        if (error.message.includes('already registered')) {
          return { success: false, message: 'This email is already registered' };
        } else if (error.message.includes('rate limit')) {
          return { success: false, message: 'Too many attempts. Please try again later' };
        } else if (error.message.includes('password')) {
          return { success: false, message: 'Password is too weak. Please use a stronger password' };
        }

        return { success: false, message: error.message };
      }

      // Check if email confirmation is required
      if (data.user?.identities && data.user.identities.length > 0) {
        const identity = data.user.identities[0];
        if (identity.identity_data && !identity.identity_data.email_verified) {
          console.log('Email verification required');

          try {
            // Send a custom verification email using Resend
            const verificationLink = redirectTo;
            await sendVerificationEmail(email, verificationLink);
            console.log('Custom verification email sent successfully');
          } catch (emailError) {
            console.error('Error sending custom verification email:', emailError);
            // Continue with Supabase's default email if our custom email fails
          }

          return {
            success: true,
            message: 'Please check your email to verify your account before signing in'
          };
        }
      }

      // If no email confirmation is required (or auto-confirmed in development)
      if (data.session) {
        console.log('Successfully signed up and signed in with email');

        try {
          // Double-check the session is valid
          const { error: sessionError } = await supabase.auth.getSession();

          if (sessionError) {
            console.error('Error validating session after sign up:', sessionError);

            // Try to set the session explicitly
            const { error: setSessionError } = await supabase.auth.setSession({
              access_token: data.session.access_token,
              refresh_token: data.session.refresh_token,
            });

            if (setSessionError) {
              console.error('Error setting session after sign up:', setSessionError);
              return {
                success: true,
                message: 'Your account has been created, but there was an issue with automatic sign-in. Please sign in manually.'
              };
            }
          }

          // Add a delay to ensure the backend has time to process the verification
          console.log('Adding a delay to ensure backend processing completes...');
          await new Promise(resolve => setTimeout(resolve, 2000));

          // Set the session and load user data
          setSession(data.session);

          // Wait for user data to load with retries
          await loadUserData();

          // Add another small delay to ensure everything is synchronized
          await new Promise(resolve => setTimeout(resolve, 1000));

          return { success: true, message: 'Successfully signed up and signed in' };
        } catch (sessionError) {
          console.error('Exception handling session after sign up:', sessionError);
          return {
            success: true,
            message: 'Your account has been created, but there was an issue with automatic sign-in. Please sign in manually.'
          };
        }
      }

      // Special handling for development/testing
      if (isDev) {
        console.log('In development mode - checking for user in data:', data.user?.id);
        console.log('Note: In development, check the Supabase logs for the confirmation email URL');
      }

      return {
        success: true,
        message: 'Successfully signed up. Please check your email to verify your account'
      };
    } catch (error) {
      console.error('Exception during email sign up:', error);
      return { success: false, message: 'An unexpected error occurred. Please try again' };
    } finally {
      setLoading(false);
    }
  };

  // Sign in anonymously (with or without CAPTCHA)
  const signInAnonymously = async (captchaToken?: string): Promise<{ success: boolean; message: string }> => {
    try {
      console.log('Signing in anonymously...');
      setLoading(true);

      // Check if we already have an anonymous session
      if (session && isAnonymous) {
        console.log('Already signed in anonymously');
        return { success: true, message: 'Already signed in anonymously' };
      }

      let data, error;

      // If a captchaToken is provided, use it
      if (captchaToken) {
        console.log('Using CAPTCHA token for anonymous sign-in, token length:', captchaToken.length);

        // Sign in anonymously with Supabase, including the CAPTCHA token
        const result = await supabase.auth.signInAnonymously({
          options: {
            captchaToken
          }
        });

        data = result.data;
        error = result.error;
      } else {
        // Try without a token first - this will work if CAPTCHA is disabled in Supabase
        console.log('No CAPTCHA token provided, trying anonymous sign-in without CAPTCHA');

        const result = await supabase.auth.signInAnonymously();
        data = result.data;
        error = result.error;
      }

      if (error) {
        console.error('Error signing in anonymously:', error);
        return { success: false, message: error.message };
      }

      if (!data.session) {
        console.error('No session returned after anonymous sign in');
        return { success: false, message: 'Anonymous authentication failed. Please try again' };
      }

      // Set session and mark as anonymous
      console.log('Successfully signed in anonymously');
      setSession(data.session);
      setIsAnonymous(true);

      // Load user data
      await loadUserData();

      return { success: true, message: 'Successfully signed in anonymously' };
    } catch (error) {
      console.error('Exception during anonymous sign in:', error);
      return { success: false, message: 'An unexpected error occurred. Please try again' };
    } finally {
      setLoading(false);
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      setLoading(true);
      console.log('Signing out user...');

      // Get the current session before signing out (for logging purposes)
      const { data: { session: currentSession } } = await supabase.auth.getSession();
      const hasSession = !!currentSession;

      console.log('Current session before sign out:', hasSession ? 'Session exists' : 'No session');

      // Sign out with scope: 'global' to invalidate all sessions across all devices
      const { error } = await supabase.auth.signOut({
        scope: 'global'
      });

      if (error) {
        console.error('Error during sign out:', error);
        Alert.alert('Error', error.message);
      } else {
        console.log('Sign out successful');

        // Clear local state
        setSession(null);
        setUser(null);
        setIsAnonymous(false);

        // Verify the session is gone
        const { data: { session: afterSession } } = await supabase.auth.getSession();
        console.log('Session after sign out:', afterSession ? 'Still exists (unexpected)' : 'Successfully removed');

        // Navigate to login screen
        router.replace('/auth/login');
      }
    } catch (error) {
      console.error('Exception during sign out:', error);
      Alert.alert('Error', 'Failed to sign out. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Reset password (send reset email)
  const resetPassword = async (email: string, captchaToken?: string): Promise<{ success: boolean; message: string }> => {
    try {
      console.log('Sending password reset email...');
      setLoading(true);

      // Validate email
      if (!email) {
        return { success: false, message: 'Email is required' };
      }

      // Determine redirect URL based on environment
      let redirectTo;

      // IMPORTANT: For password reset, we should use the direct app URL scheme
      // Supabase will handle the proper construction of the verification URL with all required tokens
      if (isExpoGo) {
        // For Expo Go, we need to use the auth.expo.io proxy
        // But we'll redirect to our reset-password screen instead of the callback
        redirectTo = `https://auth.expo.io/@chaupham1092/pilllogic/--/auth/reset-password`;
        console.log('Using Expo Go redirect URL for password reset:', redirectTo);
      } else {
        // For production, use the direct app URL scheme
        // Supabase will handle adding the necessary tokens and parameters
        // IMPORTANT: Always include the email parameter for production to ensure fallback works
        redirectTo = `pilllogic://auth/reset-password?email=${encodeURIComponent(email)}`;
        console.log('Using direct app URL scheme for password reset with email parameter:', redirectTo);
      }

      try {
        // For development in Expo Go, we'll modify the redirect URL to include the email
        // This helps with the password reset flow when the token is stripped
        if (isExpoGo && !redirectTo.includes('email=')) {
          redirectTo = `${redirectTo}?email=${encodeURIComponent(email)}`;
          console.log('Modified redirect URL for Expo Go:', redirectTo);
        }

        // Add a timestamp to help prevent caching issues
        const timestamp = new Date().getTime();
        redirectTo = `${redirectTo}${redirectTo.includes('?') ? '&' : '?'}t=${timestamp}`;
        console.log('Added timestamp to redirect URL:', redirectTo);

        // Log the full redirect URL for debugging
        console.log('Full redirect URL for password reset:', redirectTo);

        // Use Supabase to generate a password reset token
        console.log('Calling supabase.auth.resetPasswordForEmail with redirectTo and captchaToken');

        const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
          redirectTo: redirectTo,
          // Use CAPTCHA token if provided
          captchaToken: captchaToken
        });

        if (error) {
          console.error('Error generating password reset token:', error);

          // Handle specific error cases
          if (error.message.includes('rate limit')) {
            return { success: false, message: 'Too many attempts. Please try again later' };
          }

          return { success: false, message: error.message };
        }

        console.log('Password reset token generated successfully by Supabase');

        try {
          // Send a custom password reset email using our Resend template
          // This will replace Supabase's default email
          await sendPasswordResetEmail(
            email,
            // Use the same redirectTo URL that we passed to Supabase
            redirectTo
          );
          console.log('Custom password reset email sent successfully via Resend');
        } catch (emailError) {
          console.error('Error sending custom password reset email:', emailError);
          // If our custom email fails, Supabase's default email will still be sent
          console.log('Falling back to Supabase default password reset email');
        }

        return {
          success: true,
          message: 'Password reset instructions have been sent to your email'
        };
      } catch (error) {
        console.error('Exception sending password reset email:', error);
        return {
          success: false,
          message: 'An error occurred while sending the password reset email. Please try again later.'
        };
      }
    } catch (error) {
      console.error('Exception during password reset:', error);
      return { success: false, message: 'An unexpected error occurred. Please try again' };
    } finally {
      setLoading(false);
    }
  };

  // Update password (after reset)
  const updatePassword = async (password: string): Promise<{ success: boolean; message: string }> => {
    try {
      console.log('Updating password...');
      setLoading(true);

      // Validate password
      if (!password) {
        return { success: false, message: 'Password is required' };
      }

      // Validate password strength
      if (password.length < 8) {
        return { success: false, message: 'Password must be at least 8 characters long' };
      }

      // Update password
      const { error } = await supabase.auth.updateUser({
        password: password
      });

      if (error) {
        console.error('Error updating password:', error);

        // Handle specific error cases
        if (error.message.includes('password')) {
          return { success: false, message: 'Password is too weak. Please use a stronger password' };
        } else if (error.message.includes('rate limit')) {
          return { success: false, message: 'Too many attempts. Please try again later' };
        }

        return { success: false, message: error.message };
      }

      return { success: true, message: 'Password updated successfully' };
    } catch (error) {
      console.error('Exception during password update:', error);
      return { success: false, message: 'An unexpected error occurred. Please try again' };
    } finally {
      setLoading(false);
    }
  };



  // Auth context value
  const value = {
    session,
    user,
    loading,
    isAnonymous,
    signInWithGoogle,
    signInWithApple,
    signInWithEmail,
    signUpWithEmail,
    signInAnonymously,
    resetPassword,
    updatePassword,
    signOut,
    refreshUser,
  };

  // Render the auth provider
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
